import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import API from '../../api';

const quickLinks = [
  { label: 'Dashboard', to: '/dashboard', icon: '🏠' },
  { label: 'Create Job', to: '/job-create', icon: '➕' },
  { label: 'Aptitude', to: '/aptitude', icon: '📝' },
  { label: 'Interview', to: '/interview', icon: '🎤' },
];

const defaultRoles = [
  'Frontend Developer',
  'Backend Developer',
  'Data Analyst',
  'HR Manager',
  'Product Manager',
];

const CompanyDashboard = () => {
  const [stats, setStats] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const token = localStorage.getItem('token'); // or sessionStorage

    API.get('/api/company/dashboard', {
      headers: { Authorization: `Bearer ${token}` },
    })
      .then(({ data }) => {
        console.log('✅ Dashboard data:', data);
        setStats([
          { label: 'No. of vacancy', value: data.totalJobs || 0 },
          { label: 'No. of job position', value: data.activeJobs || 0 },
          { label: 'No. of response', value: data.applications || 0 },
        ]);
        setLoading(false);
      })
      .catch(err => {
        console.error('❌ Fetch error:', err);
        setError(err.response?.data?.message || err.message);
        setLoading(false);
      });
  }, []);

  return (
    <div className="w-full">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {stats.map((stat, i) => (
          <motion.div
            key={stat.label}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 + i * 0.15, type: 'spring', stiffness: 80 }}
            whileHover={{ scale: 1.02, y: -4, boxShadow: '0 8px 25px 0 rgba(35,65,75,0.12)' }}
            className="bg-white border border-gray-200 shadow-lg p-6 flex flex-col items-center rounded-xl cursor-pointer transition-all duration-300 hover:border-[rgb(35,65,75)]/20"
          >
            <div className="w-12 h-12 bg-gradient-to-br from-[rgb(35,65,75)] to-gray-700 rounded-xl flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                {i === 0 && <path strokeLinecap="round" strokeLinejoin="round" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6" />}
                {i === 1 && <path strokeLinecap="round" strokeLinejoin="round" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />}
                {i === 2 && <path strokeLinecap="round" strokeLinejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />}
              </svg>
            </div>
            <div className="text-3xl font-bold text-[rgb(35,65,75)] mb-2">{stat.value}</div>
            <div className="text-sm font-medium text-gray-600 text-center">{stat.label}</div>
          </motion.div>
        ))}
      </div>
      {/* Available Jobs/Roles */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-gradient-to-br from-[rgb(35,65,75)] to-gray-700 rounded-lg flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6" />
            </svg>
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">Available Job Roles</h2>
            <p className="text-sm text-gray-600">Explore the roles your company is hiring for</p>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {defaultRoles.map((role, idx) => (
            <motion.div
              key={role}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: idx * 0.1 }}
              whileHover={{ scale: 1.02, y: -2 }}
              className="bg-gray-50 border border-gray-200 p-4 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md hover:border-[rgb(35,65,75)]/30 hover:bg-white"
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-[rgb(35,65,75)]/10 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-[rgb(35,65,75)]" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="10" />
                  </svg>
                </div>
                <div className="font-medium text-gray-900">{role}</div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-gradient-to-br from-[rgb(35,65,75)] to-gray-700 rounded-lg flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">Quick Actions</h2>
            <p className="text-sm text-gray-600">Frequently used features and shortcuts</p>
          </div>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4">
          {quickLinks.map((link, i) => (
            link.label === 'Interview' ? (
              <motion.div
                key={link.to}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: i * 0.1 }}
                className="group"
              >
                <a
                  href="https://interviewpage-inky.vercel.app/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex flex-col items-center p-4 bg-gray-50 border border-gray-200 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md hover:border-[rgb(35,65,75)]/30 hover:bg-white text-center"
                >
                  <div className="w-10 h-10 bg-[rgb(35,65,75)]/10 rounded-lg flex items-center justify-center mb-3 group-hover:bg-[rgb(35,65,75)]/20 transition-colors">
                    <span className="text-[rgb(35,65,75)] text-lg">{link.icon}</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900 group-hover:text-[rgb(35,65,75)] transition-colors">
                    {link.label}
                  </span>
                </a>
              </motion.div>
            ) : (
              <motion.div
                key={link.to}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: i * 0.1 }}
                className="group"
              >
                <Link
                  to={link.to}
                  className="flex flex-col items-center p-4 bg-gray-50 border border-gray-200 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md hover:border-[rgb(35,65,75)]/30 hover:bg-white text-center"
                >
                  <div className="w-10 h-10 bg-[rgb(35,65,75)]/10 rounded-lg flex items-center justify-center mb-3 group-hover:bg-[rgb(35,65,75)]/20 transition-colors">
                    <span className="text-[rgb(35,65,75)] text-lg">{link.icon}</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900 group-hover:text-[rgb(35,65,75)] transition-colors">
                    {link.label}
                  </span>
                </Link>
              </motion.div>
            )
          ))}
        </div>
      </div>
    </div>
  );
};

export default CompanyDashboard;