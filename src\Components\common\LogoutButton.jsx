import React, { useState } from 'react';
import { LogOut, AlertTriangle } from 'lucide-react';
import { FaSignOutAlt } from 'react-icons/fa';
import useAuth from '../../hooks/useAuth';

/**
 * Reusable Logout Button Component
 * @param {Object} props
 * @param {string} props.variant - Button style variant ('sidebar', 'navbar', 'button', 'icon')
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.showConfirmation - Whether to show confirmation dialog
 * @param {Function} props.onLogoutStart - Callback when logout starts
 * @param {Function} props.onLogoutComplete - Callback when logout completes
 * @param {string} props.iconType - Icon type ('lucide' or 'fa')
 */
const LogoutButton = ({
  variant = 'button',
  className = '',
  showConfirmation = false,
  onLogoutStart,
  onLogoutComplete,
  iconType = 'lucide'
}) => {
  const { logout, loading } = useAuth();
  const [showDialog, setShowDialog] = useState(false);

  const handleLogout = async () => {
    if (showConfirmation && !showDialog) {
      setShowDialog(true);
      return;
    }

    try {
      if (onLogoutStart) onLogoutStart();
      await logout();
      if (onLogoutComplete) onLogoutComplete();
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setShowDialog(false);
    }
  };

  const handleCancel = () => {
    setShowDialog(false);
  };

  // Icon component based on type
  const IconComponent = iconType === 'fa' ? FaSignOutAlt : LogOut;

  // Base button styles for different variants
  const getVariantStyles = () => {
    switch (variant) {
      case 'sidebar':
        return 'w-full flex items-center gap-3 text-gray-300 hover:text-white hover:bg-red-600/20 rounded-xl px-4 py-3 transition-all duration-200 group';
      
      case 'navbar':
        return 'flex items-center gap-2 bg-red-500 text-white px-4 py-2 rounded-full font-medium shadow hover:scale-105 transition-transform duration-300';
      
      case 'icon':
        return 'p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200';
      
      case 'button':
      default:
        return 'flex items-center gap-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200';
    }
  };

  const buttonStyles = `${getVariantStyles()} ${className} ${loading ? 'opacity-50 cursor-not-allowed' : ''}`;

  return (
    <>
      <button
        onClick={handleLogout}
        disabled={loading}
        className={buttonStyles}
        title="Logout"
      >
        <IconComponent 
          className={`${variant === 'sidebar' ? 'w-5 h-5 group-hover:text-red-400' : 'w-4 h-4'}`} 
        />
        {variant !== 'icon' && (
          <span className="font-medium">
            {loading ? 'Logging out...' : 'Logout'}
          </span>
        )}
      </button>

      {/* Confirmation Dialog */}
      {showDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-sm mx-4">
            <div className="flex items-center gap-3 mb-4">
              <AlertTriangle className="w-6 h-6 text-amber-500" />
              <h3 className="text-lg font-semibold text-gray-900">Confirm Logout</h3>
            </div>
            
            <p className="text-gray-600 mb-6">
              Are you sure you want to logout? You will need to login again to access your account.
            </p>
            
            <div className="flex gap-3 justify-end">
              <button
                onClick={handleCancel}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleLogout}
                disabled={loading}
                className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors disabled:opacity-50"
              >
                {loading ? 'Logging out...' : 'Logout'}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

/**
 * Sidebar-specific logout button
 */
export const SidebarLogoutButton = (props) => (
  <LogoutButton variant="sidebar" {...props} />
);

/**
 * Navbar-specific logout button
 */
export const NavbarLogoutButton = (props) => (
  <LogoutButton variant="navbar" {...props} />
);

/**
 * Icon-only logout button
 */
export const IconLogoutButton = (props) => (
  <LogoutButton variant="icon" {...props} />
);

/**
 * Standard logout button
 */
export const StandardLogoutButton = (props) => (
  <LogoutButton variant="button" {...props} />
);

export default LogoutButton;
