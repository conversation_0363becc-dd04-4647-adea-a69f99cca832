import React from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';

const AddUserForm = ({ form, success, error, handleChange, handleSubmit, setShowForm }) => {
  const fieldVariants = {
    hidden: { opacity: 0, scale: 1.1, y: 15 },
    visible: (i) => ({ opacity: 1, scale: 1, y: 0, transition: { delay: 0.1 + i * 0.1, duration: 0.4, type: 'spring', stiffness: 90 } })
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <motion.div
        className="relative bg-white rounded-2xl shadow-2xl p-8 w-full max-w-xl border-2 border-blue-200"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        <button
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-800 transition-colors"
          onClick={() => setShowForm(false)}
          aria-label="Close"
        >
          <XMarkIcon className="w-7 h-7" />
        </button>
        <form className="flex flex-col gap-5" onSubmit={handleSubmit}>
          <h2 className="text-2xl font-bold text-center text-gray-800 mb-2">User Details</h2>
          {[
            { label: 'Name', name: 'name', type: 'text', required: true },
            { label: 'Email', name: 'email', type: 'email', required: true },
            { label: 'Role', name: 'role', type: 'select', options: ['Admin', 'Manager', 'Viewer'], required: true }
          ].map((field, idx) => (
            <motion.div
              key={field.name}
              className="flex flex-col gap-1"
              custom={idx}
              initial="hidden"
              animate="visible"
              variants={fieldVariants}
            >
              <label className="block font-semibold text-gray-700">{field.label}</label>
              {field.type === 'select' ? (
                <select
                  name={field.name}
                  value={form[field.name]}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-300 focus:outline-none bg-gray-50 transition"
                >
                  {field.options.map(opt => <option key={opt} value={opt}>{opt}</option>)}
                </select>
              ) : (
                <input
                  type={field.type}
                  name={field.name}
                  value={form[field.name]}
                  onChange={handleChange}
                  required={field.required}
                  className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-300 focus:outline-none bg-gray-50 transition"
                />
              )}
            </motion.div>
          ))}
          <motion.button
            type="submit"
            className="mt-4 px-8 py-3 text-white rounded-xl font-bold shadow transition-colors text-lg"
            style={{ background: 'rgb(35, 65, 75)' }}
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.98 }}
            custom={3}
            initial="hidden"
            animate="visible"
            variants={fieldVariants}
          >
            Submit User
          </motion.button>
          {success && (
            <motion.div
              className="text-green-600 font-semibold mt-2 text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              User added successfully!
            </motion.div>
          )}
          {error && (
            <motion.div
              className="text-red-600 font-semibold mt-2 text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              {error}
            </motion.div>
          )}
        </form>
      </motion.div>
    </div>
  );
};

export default AddUserForm; 