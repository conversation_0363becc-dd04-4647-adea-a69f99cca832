import React, { useState, useEffect } from 'react';
import { FaSearch, FaShareAlt, FaCheckCircle, FaBriefcase, FaBook, FaCertificate } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import useStudentStore from '../../store/studentStore';

const tabs = [
  'All',
  'Applied',
  'Test Pending',
  'Test Completed',
  'Interview',
  'Selected',
  'Rejected'
];

const filterOptions = ['All', 'applied', 'test_pending', 'test_completed', 'interview', 'selected', 'rejected'];
const sortOptions = ['Newest', 'Oldest'];

const formatDate = (iso) => {
  const d = new Date(iso);
  return d.toLocaleString('en-GB', { day: '2-digit', month: 'short', year: '2-digit', hour: '2-digit', minute: '2-digit', hour12: true }) + ' IST';
};

const Applications = () => {
  const [activeTab, setActiveTab] = useState('All');
  const [search, setSearch] = useState('');
  const [filter, setFilter] = useState('All');
  const [sort, setSort] = useState('Newest');
  const navigate = useNavigate();

  const {
    profile,
    applications,
    applicationsLoading,
    applicationsError,
    fetchApplications,
    getProfileCompletion
  } = useStudentStore();

  // Fetch applications on component mount
  useEffect(() => {
    fetchApplications();
  }, [fetchApplications]);

  // Use applications data from store
  const apps = applications || [];
  const profileCompletion = getProfileCompletion();

  // Helper function to map status to display format
  const getStatusDisplay = (status) => {
    const statusMap = {
      'applied': 'Applied',
      'test_pending': 'Test Pending',
      'test_completed': 'Test Completed',
      'interview': 'Interview',
      'selected': 'Selected',
      'rejected': 'Rejected'
    };
    return statusMap[status] || status;
  };

  // Helper function to get status color with enhanced styling
  const getStatusColor = (status) => {
    const colorMap = {
      'applied': 'bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100',
      'test_pending': 'bg-yellow-50 text-yellow-700 border-yellow-200 hover:bg-yellow-100',
      'test_completed': 'bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100',
      'interview': 'bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100',
      'selected': 'bg-green-50 text-green-700 border-green-200 hover:bg-green-100',
      'rejected': 'bg-red-50 text-red-700 border-red-200 hover:bg-red-100'
    };
    return colorMap[status] || 'bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100';
  };

  // Filter and sort applications
  let filteredApps = apps.filter(app => {
    // Tab filtering - match by status
    const matchesTab = activeTab === 'All' ||
      (activeTab === 'Applied' && app.application?.status === 'applied') ||
      (activeTab === 'Test Pending' && app.application?.status === 'test_pending') ||
      (activeTab === 'Test Completed' && app.application?.status === 'test_completed') ||
      (activeTab === 'Interview' && app.application?.status === 'interview') ||
      (activeTab === 'Selected' && app.application?.status === 'selected') ||
      (activeTab === 'Rejected' && app.application?.status === 'rejected');

    // Search filtering - search in job title and company name
    const matchesSearch = search === '' ||
      app.title?.toLowerCase().includes(search.toLowerCase()) ||
      app.company?.name?.toLowerCase().includes(search.toLowerCase());

    // Status filtering
    const matchesFilter = filter === 'All' || app.application?.status === filter;

    return matchesTab && matchesSearch && matchesFilter;
  });

  // Sort applications
  filteredApps = filteredApps.sort((a, b) => {
    const dateA = new Date(a.application?.appliedAt || a.createdAt);
    const dateB = new Date(b.application?.appliedAt || b.createdAt);

    if (sort === 'Newest') {
      return dateB - dateA;
    } else {
      return dateA - dateB;
    }
  });

  // Show loading state
  if (applicationsLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <div className="text-lg text-gray-600">Loading applications...</div>
        </div>
      </div>
    );
  }

  // Show error state
  if (applicationsError) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <div className="text-xl text-red-600 mb-2">Error loading applications</div>
          <div className="text-gray-600 mb-4">{applicationsError}</div>
          <button
            onClick={() => fetchApplications()}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20">
      {/* Enhanced Header Section */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            {/* Title and User Info */}
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center text-white font-bold text-lg shadow-lg">
                {profile.avatar ? (
                  <img src={profile.avatar} alt="avatar" className="w-full h-full object-cover rounded-xl" />
                ) : (
                  profile.name?.split(' ').map(n => n[0]).join('').toUpperCase() || 'U'
                )}
              </div>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">My Applications</h1>
                <p className="text-gray-600 mt-1">Track your job applications and their progress</p>
              </div>
            </div>

            {/* Profile Completion Badge */}
            <div className="flex items-center gap-3 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl px-4 py-3">
              <div className="flex flex-col items-center">
                <div className="text-sm font-medium text-gray-700 mb-1">Profile</div>
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-full bg-white border-2 border-green-200 flex items-center justify-center">
                    <div className="w-6 h-6 rounded-full bg-green-500 flex items-center justify-center">
                      <span className="text-xs font-bold text-white">{profileCompletion || 0}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Enhanced Tabs Section */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
              <div>
                <h2 className="text-xl font-bold text-gray-900 mb-2">Filter Applications</h2>
                <p className="text-gray-600 text-sm">Use filters to find specific applications quickly</p>
              </div>

              {/* Stats Summary */}
              <div className="flex items-center gap-4 text-sm">
                <div className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full font-medium">
                  Total: {apps.length}
                </div>
                <div className="bg-green-50 text-green-700 px-3 py-1 rounded-full font-medium">
                  Active: {filteredApps.length}
                </div>
              </div>
            </div>

            {/* Enhanced Tabs */}
            <div className="flex flex-wrap gap-2 mb-6">
              {tabs.map(tab => (
                <button
                  key={tab}
                  className={`px-4 py-2 rounded-xl font-semibold transition-all duration-200 ${
                    activeTab === tab
                      ? 'bg-blue-600 text-white shadow-lg shadow-blue-200'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:text-gray-900'
                  }`}
                  onClick={() => setActiveTab(tab)}
                >
                  {tab}
                </button>
              ))}
            </div>

            {/* Enhanced Filters and Search */}
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search applications by job title or company..."
                    value={search}
                    onChange={e => setSearch(e.target.value)}
                    className="w-full pl-12 pr-4 py-3 rounded-xl border border-gray-300 focus:border-blue-500 focus:ring-4 focus:ring-blue-100 outline-none transition-all duration-200 bg-gray-50 focus:bg-white"
                  />
                  <FaSearch className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" />
                </div>
              </div>

              {/* Filter Buttons */}
              <div className="flex flex-wrap gap-2">
                {filterOptions.slice(0, 4).map(opt => (
                  <button
                    key={opt}
                    className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 ${
                      filter === opt
                        ? 'bg-indigo-600 text-white shadow-lg shadow-indigo-200'
                        : 'bg-white border border-gray-300 text-gray-700 hover:border-indigo-300 hover:text-indigo-600'
                    }`}
                    onClick={() => setFilter(opt)}
                  >
                    {opt === 'All' ? 'All Status' : getStatusDisplay(opt)}
                  </button>
                ))}

                {/* Sort Dropdown */}
                <div className="relative">
                  <select
                    value={sort}
                    onChange={e => setSort(e.target.value)}
                    className="appearance-none px-4 py-3 rounded-xl border border-gray-300 font-medium text-gray-700 bg-white focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 cursor-pointer outline-none transition-all duration-200 pr-10"
                  >
                    {sortOptions.map(opt => (
                      <option key={opt} value={opt}>{opt}</option>
                    ))}
                  </select>
                  <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </span>
                </div>
              </div>
            </div>
          </div>
          {/* Results Header */}
          {filteredApps.length > 0 && (
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <h3 className="text-lg font-semibold text-gray-900">
                  {filteredApps.length} Application{filteredApps.length !== 1 ? 's' : ''} Found
                </h3>
                {activeTab !== 'All' && (
                  <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                    {activeTab}
                  </span>
                )}
              </div>

              {/* Quick Actions */}
              <div className="flex items-center gap-2">
                <button
                  onClick={() => {
                    setActiveTab('All');
                    setFilter('All');
                    setSearch('');
                  }}
                  className="text-sm text-gray-600 hover:text-gray-900 px-3 py-1 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          )}

          {/* Application Cards */}
          <div className="space-y-4">
            {filteredApps.length === 0 ? (
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border border-blue-100 p-12 text-center">
                <div className="w-20 h-20 mx-auto mb-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <div className="text-blue-500 text-3xl">📋</div>
                </div>
                <div className="text-2xl font-bold text-gray-800 mb-3">No applications found</div>
                <div className="text-gray-600 mb-8 max-w-lg mx-auto leading-relaxed">
                  {apps.length === 0
                    ? "Ready to kickstart your career? Explore amazing job opportunities and take the first step towards your dream job!"
                    : "No applications match your current filters. Try adjusting your search criteria or explore different categories."
                  }
                </div>
                {apps.length === 0 && (
                  <button
                    onClick={() => navigate('/student/jobs')}
                    className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-4 rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  >
                    🚀 Explore Jobs
                  </button>
                )}
              </div>
            ) : (
              filteredApps.map(app => (
                <div
                  key={app._id}
                  className="group bg-white rounded-2xl shadow-sm border border-gray-200 p-6 flex flex-col lg:flex-row lg:items-center gap-6 cursor-pointer hover:shadow-xl hover:border-blue-300 hover:bg-gradient-to-r hover:from-blue-50/30 hover:to-indigo-50/30 transition-all duration-300 min-h-[140px] relative overflow-hidden"
                  onClick={() => navigate(`/student/applications/${app._id}`)}
                >
                  {/* Subtle background pattern */}
                  <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-gray-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  {/* Content wrapper */}
                  <div className="relative z-10 flex flex-col lg:flex-row lg:items-center gap-6 w-full">
                    {/* Left Section: Company Logo + Job Info */}
                    <div className="flex items-center gap-4 flex-1 min-w-0">
                      {/* Enhanced Company Logo */}
                      <div className="relative">
                        <div className="w-16 h-16 lg:w-20 lg:h-20 rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-blue-100 flex items-center justify-center flex-shrink-0 group-hover:border-blue-200 transition-colors duration-300">
                          {app.company?.logo ? (
                            <img
                              src={app.company.logo}
                              alt={app.company.name}
                              className="w-10 h-10 lg:w-12 lg:h-12 rounded-xl object-contain"
                            />
                          ) : (
                            <div className="text-xl lg:text-2xl font-bold text-blue-500">
                              {app.company?.name?.charAt(0) || app.title?.charAt(0) || '?'}
                            </div>
                          )}
                        </div>
                        {/* Status indicator dot */}
                        <div className={`absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                          app.application?.status === 'selected' ? 'bg-green-500' :
                          app.application?.status === 'rejected' ? 'bg-red-500' :
                          app.application?.status === 'test_pending' ? 'bg-yellow-500' :
                          'bg-blue-500'
                        }`}></div>
                      </div>

                      {/* Enhanced Job Information */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1 min-w-0">
                            <h3 className="font-bold text-lg lg:text-xl text-gray-900 truncate mb-1 group-hover:text-blue-700 transition-colors duration-200">
                              {app.title}
                            </h3>
                            <div className="flex items-center gap-2 text-gray-600 text-sm mb-3">
                              <span className="font-semibold">{app.company?.name || 'Unknown Company'}</span>
                              {app.location && (
                                <>
                                  <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                                  <span className="flex items-center gap-1">
                                    📍 {app.location}
                                  </span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Enhanced Application Details */}
                        <div className="flex flex-wrap items-center gap-3 text-sm">
                          {app.application?.appliedAt && (
                            <div className="flex items-center gap-1 text-gray-600 bg-gray-50 px-3 py-1 rounded-full">
                              <span className="text-xs">📅</span>
                              <span>Applied: {formatDate(app.application.appliedAt)}</span>
                            </div>
                          )}
                          {app.application?.testScore && (
                            <div className="flex items-center gap-1 text-blue-700 bg-blue-50 px-3 py-1 rounded-full">
                              <span className="text-xs">🎯</span>
                              <span className="font-semibold">Score: {app.application.testScore}%</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Right Section: Enhanced Status + Actions */}
                    <div className="flex flex-row lg:flex-col items-center lg:items-end justify-between lg:justify-center gap-4 lg:gap-4 lg:min-w-[180px]">
                      {/* Enhanced Status Badge */}
                      <div className="flex flex-col lg:items-end gap-2">
                        <span className={`px-4 py-2 rounded-xl text-sm font-bold whitespace-nowrap shadow-sm border-2 transition-all duration-200 ${getStatusColor(app.application?.status)} group-hover:shadow-md`}>
                          {app.application?.status === 'selected' && <FaCheckCircle className="inline mr-2" />}
                          {getStatusDisplay(app.application?.status)}
                        </span>

                        {/* Progress indicator */}
                        <div className="hidden lg:flex items-center gap-1">
                          {['applied', 'test_pending', 'test_completed', 'interview', 'selected'].map((step, index) => (
                            <div
                              key={step}
                              className={`w-2 h-2 rounded-full transition-colors duration-200 ${
                                ['applied', 'test_pending', 'test_completed', 'interview', 'selected'].indexOf(app.application?.status) >= index
                                  ? 'bg-blue-500'
                                  : 'bg-gray-200'
                              }`}
                            ></div>
                          ))}
                        </div>
                      </div>

                      {/* Enhanced Actions and Info */}
                      <div className="flex flex-col items-end gap-3">
                        <div className="flex items-center gap-2">
                          <button
                            className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                            onClick={e => e.stopPropagation()}
                            title="Share application"
                          >
                            <FaShareAlt className="text-sm" />
                          </button>
                          <button
                            className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-all duration-200"
                            onClick={e => e.stopPropagation()}
                            title="View details"
                          >
                            <FaCheckCircle className="text-sm" />
                          </button>
                        </div>

                        {app.applicationDeadline && (
                          <div className="text-xs text-gray-500 text-right bg-gray-50 px-3 py-2 rounded-lg">
                            <div className="font-medium text-gray-700">Deadline</div>
                            <div className="font-semibold">{formatDate(app.applicationDeadline)}</div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default Applications; 