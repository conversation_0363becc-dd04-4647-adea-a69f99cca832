import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { FaCheckCircle, FaArrowLeft, FaShareAlt, FaUser, FaCalendarAlt, FaSpinner, FaExclamationTriangle, FaBriefcase, FaBuilding, FaMapMarkerAlt, FaClock } from 'react-icons/fa';
import useStudentStore from '../../../store/studentStore';
import toast from 'react-hot-toast';

// Static data - replaced with dynamic API data
const mockApplications = [
  {
    id: 1,
    title: 'Direct Sales Executive',
    company: 'MEDINI',
    logo: 'https://logo.clearbit.com/MEDINI.COM',
    updatedOn: 'Jan 10, 2025',
    registeredOn: '07 Jan 25, 06:45 PM IST',
    deadline: '10 Jan 25, 07:44 AM IST',
    status: 'Closed',
    steps: ['Registration Form', 'Extended Form'],
    completedSteps: [true, true],
    notifications: false,
    completed: true,
    email: '<EMAIL>',
    category: 'Jobs',
    eligibility: 'Everyone can apply',
    description: `**MEDINI is seeking a highly motivated and results-oriented Direct Sales Executive to join our growing team!**\n\n**Responsibilities of the Candidate:**\n- Prospect and qualify potential clients through various channels, including phone, email, and social media.\n- Develop and maintain strong relationships with clients to understand their needs and build trust.\n- Present and demonstrate the value proposition of Hire Mate's products and services.\n- Negotiate and close deals, exceeding individual and team sales targets.\n- Provide excellent customer service and support to ensure client satisfaction.\n- Stay up-to-date on industry trends and competitor offerings.\n- Contribute to the development and implementation of sales strategies and initiatives.\n- Maintain accurate records and reports of sales activities and results.`,
    applied: 560,
    impressions: 44544,
    refer: true,
    user: {
      name: 'Abhishek E B',
      email: '<EMAIL>',
      eligible: true,
      avatar: '',
    },
  },
  {
    id: 2,
    title: 'Python Internship',
    company: 'MEDINI',
    logo: 'https://logo.clearbit.com/MEDINI.COM',
    updatedOn: 'Nov 15, 2024',
    registeredOn: '26 Sep 24, 10:26 PM IST',
    deadline: '15 Nov 24, 11:59 PM IST',
    status: 'Closed',
    steps: ['Registration Form', 'Extended Form'],
    completedSteps: [true, true],
    notifications: true,
    completed: true,
    email: '<EMAIL>',
    category: 'Internships',
    eligibility: 'Everyone can apply',
    description: `**Internship Mela is looking for Python Interns!**\n\n**Responsibilities:**\n- Assist in developing Python applications.\n- Collaborate with the team on new features.\n- Write clean, maintainable code.\n- Participate in code reviews.\n- Learn and apply new technologies.`,
    applied: 120,
    impressions: 12000,
    refer: true,
    user: {
      name: 'Abhishek E B',
      email: '<EMAIL>',
      eligible: true,
      avatar: '',
    },
  },
  {
    id: 3,
    title: 'MEDINI Hiring Challenge 2024',
    company: 'MEDINI',
    logo: 'https://logo.clearbit.com/MEDINI.COM',
    updatedOn: 'Sep 19, 2024',
    registeredOn: '13 Sep 24, 04:23 PM IST',
    deadline: '19 Sep 24, 11:59 PM IST',
    status: 'Closed',
    steps: ['Registration Form', 'Face Verification'],
    completedSteps: [true, true],
    notifications: true,
    completed: true,
    email: '<EMAIL>',
    category: 'Competitions',
    eligibility: 'Everyone can apply',
    description: `**MEDINI is conducting a Hiring Challenge for 2024!**\n\n**Responsibilities:**\n- Solve algorithmic problems.\n- Participate in online coding rounds.\n- Face verification for shortlisted candidates.\n- Collaborate with mentors and peers.`,
    applied: 300,
    impressions: 25000,
    refer: true,
    user: {
      name: 'Abhishek E B',
      email: '<EMAIL>',
      eligible: true,
      avatar: '',
    },
  },
  {
    id: 4,
    title: 'MEDINI Imagination Challenge 2024: Student Track',
    company: 'MEDINI',
    logo: 'https://logo.clearbit.com/MEDINI.COM',
    updatedOn: 'Sep 9, 2024',
    registeredOn: '25 Aug 24, 07:39 PM IST',
    deadline: '09 Sep 24, 11:59 PM IST',
    status: 'Closed',
    steps: ['Registration Form', 'Face Verification'],
    completedSteps: [true, true],
    notifications: true,
    completed: true,
    email: '<EMAIL>',
    category: 'Competitions',
    eligibility: 'Everyone can apply',
    description: `**MEDINI Imagination Challenge 2024: Student Track**\n\n**Responsibilities:**\n- Register and complete the face verification.\n- Submit your innovative ideas.\n- Participate in the challenge rounds.\n- Network with industry leaders.`,
    applied: 200,
    impressions: 18000,
    refer: true,
    user: {
      name: 'Abhishek E B',
      email: '<EMAIL>',
      eligible: true,
      avatar: '',
    },
  },
];

const tabs = [
  'Job Description',
  'Dates & Deadlines',
  'Reviews',
  'FAQs & Discussions',
];

export default function ApplicationDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('details');

  const {
    applicationDetails,
    loading,
    error,
    profile,
    fetchApplicationDetails
  } = useStudentStore();

  // Fetch application details on component mount
  useEffect(() => {
    console.log('ApplicationDetails component mounted with ID:', id);
    if (id) {
      console.log('Fetching application details for ID:', id);
      fetchApplicationDetails(id);
    } else {
      console.log('No application ID provided');
    }
  }, [id, fetchApplicationDetails]);

  // Format status for display
  const formatStatus = (status) => {
    if (!status) return 'Unknown';
    return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'applied':
        return 'bg-blue-100 text-blue-800';
      case 'test_pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'test_completed':
        return 'bg-purple-100 text-purple-800';
      case 'shortlisted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'hired':
        return 'bg-emerald-100 text-emerald-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <FaSpinner className="animate-spin text-4xl text-blue-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Loading Application Details...</h2>
          <p className="text-gray-500">Please wait while we fetch your application information.</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto">
          <FaExclamationTriangle className="text-4xl text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Error Loading Application</h2>
          <p className="text-gray-500 mb-4">{error}</p>
          <div className="flex gap-3 justify-center">
            <button
              onClick={() => fetchApplicationDetails(id)}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition"
            >
              Try Again
            </button>
            <button
              onClick={() => navigate('/student/applications')}
              className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition"
            >
              Back to Applications
            </button>
          </div>
        </div>
      </div>
    );
  }

  // No application data
  if (!applicationDetails) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <FaBriefcase className="text-6xl text-gray-300 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Application Not Found</h2>
          <p className="text-gray-500 mb-4">The application you're looking for doesn't exist or has been removed.</p>
          <button
            onClick={() => navigate('/student/applications')}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition"
          >
            Back to Applications
          </button>
        </div>
      </div>
    );
  }

  const app = applicationDetails;

  return (
    <div className="min-h-screen bg-[#23414c]/5 py-6 px-2 md:px-8">
      <div className="max-w-6xl mx-auto flex flex-col md:flex-row gap-8">
        {/* Main Content */}
        <div className="flex-1 min-w-0">
          <button onClick={() => navigate(-1)} className="flex items-center gap-2 text-[#23414c] font-semibold mb-6 hover:underline"><FaArrowLeft /> Back</button>
          <div className="bg-white rounded-xl shadow p-6 mb-6">
            <div className="flex items-center gap-4 mb-4">
              {app.job?.companyId?.logo ? (
                <img
                  src={app.job.companyId.logo}
                  alt={app.job.companyId.name}
                  className="w-20 h-20 rounded bg-white border object-contain"
                />
              ) : (
                <div className="w-20 h-20 rounded bg-blue-100 border flex items-center justify-center">
                  <FaBuilding className="text-blue-600 text-2xl" />
                </div>
              )}
              <div className="flex-1">
                <div className="font-bold text-3xl text-gray-800 mb-1">{app.job?.title}</div>
                <div className="flex items-center gap-4 text-gray-500 text-sm mb-2">
                  <div className="flex items-center gap-1">
                    <FaBuilding className="text-sm" />
                    <span>{app.job?.companyId?.name}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <FaMapMarkerAlt className="text-sm" />
                    <span>{app.job?.location}</span>
                  </div>
                </div>
                <div className="flex items-center gap-4 text-sm">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(app.status)}`}>
                    {formatStatus(app.status)}
                  </span>
                  <span className="flex items-center gap-1 text-gray-500">
                    <FaCalendarAlt className="inline" />
                    Applied: {new Date(app.appliedAt).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
            {/* Tabs */}
            <div className="flex gap-4 border-b mb-4 mt-4">
              {['details', 'job-info', 'timeline'].map(tab => (
                <button
                  key={tab}
                  className={`pb-2 px-2 font-semibold whitespace-nowrap border-b-2 transition-all ${activeTab === tab ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-blue-600'}`}
                  onClick={() => setActiveTab(tab)}
                >
                  {tab === 'details' ? 'Application Details' :
                   tab === 'job-info' ? 'Job Information' :
                   'Application Timeline'}
                </button>
              ))}
            </div>
            {/* Tab Content */}
            {activeTab === 'details' && (
              <div className="space-y-6">
                <div className="bg-blue-50 rounded-lg p-4">
                  <h3 className="font-bold text-lg mb-3 text-gray-800">Application Status</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm text-gray-600">Current Status</div>
                      <div className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(app.status)}`}>
                        {formatStatus(app.status)}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600">Applied Date</div>
                      <div className="font-medium text-gray-800">
                        {new Date(app.appliedAt).toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </div>
                    </div>
                    {app.testScore && (
                      <div>
                        <div className="text-sm text-gray-600">Test Score</div>
                        <div className="font-medium text-gray-800">{app.testScore}%</div>
                      </div>
                    )}
                  </div>
                </div>

                {app.job?.hasTest && (
                  <div className="bg-yellow-50 rounded-lg p-4">
                    <h3 className="font-bold text-lg mb-3 text-gray-800">Assessment Information</h3>
                    <div className="text-gray-700">
                      This position requires completing an assessment test.
                      {app.status === 'test_pending' && (
                        <span className="text-yellow-600 font-medium"> Please complete your test to proceed.</span>
                      )}
                      {app.status === 'test_completed' && (
                        <span className="text-green-600 font-medium"> Test completed successfully!</span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'job-info' && (
              <div className="space-y-6">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-bold text-lg mb-3 text-gray-800">Job Description</h3>
                  <p className="text-gray-700 leading-relaxed">{app.job?.description}</p>

                  {app.job?.requirements && app.job.requirements.length > 0 && (
                    <div className="mt-4">
                      <h4 className="font-semibold mb-2 text-gray-800">Requirements</h4>
                      <ul className="list-disc pl-6 text-gray-700 space-y-1">
                        {app.job.requirements.map((req, index) => (
                          <li key={index}>{req}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {app.job?.techStack && app.job.techStack.length > 0 && (
                    <div className="mt-4">
                      <h4 className="font-semibold mb-2 text-gray-800">Tech Stack</h4>
                      <div className="flex flex-wrap gap-2">
                        {app.job.techStack.map((tech, index) => (
                          <span key={index} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <FaClock className="text-blue-500" />
                      <span className="font-medium text-gray-800">Work Details</span>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div><span className="text-gray-600">Type:</span> {app.job?.jobType}</div>
                      <div><span className="text-gray-600">Mode:</span> {app.job?.workMode}</div>
                      <div><span className="text-gray-600">Experience:</span> {app.job?.experienceLevel}</div>
                    </div>
                  </div>

                  {app.job?.salary && (
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-medium text-gray-800">Salary</span>
                      </div>
                      <div className="text-green-600 font-medium">
                        {typeof app.job.salary === 'object'
                          ? `${app.job.salary.currency || 'INR'} ${app.job.salary.min || 0} - ${app.job.salary.max || 0}`
                          : app.job.salary
                        }
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'timeline' && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-bold text-lg mb-4 text-gray-800">Application Timeline</h3>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-3 h-3 bg-blue-500 rounded-full mt-1"></div>
                    <div>
                      <div className="font-medium text-gray-800">Application Submitted</div>
                      <div className="text-sm text-gray-600">
                        {new Date(app.appliedAt).toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </div>
                    </div>
                  </div>

                  {app.status !== 'applied' && (
                    <div className="flex items-start gap-3">
                      <div className={`w-3 h-3 rounded-full mt-1 ${
                        app.status === 'test_pending' ? 'bg-yellow-500' :
                        app.status === 'test_completed' ? 'bg-purple-500' :
                        app.status === 'shortlisted' ? 'bg-green-500' :
                        app.status === 'rejected' ? 'bg-red-500' :
                        app.status === 'hired' ? 'bg-emerald-500' :
                        'bg-gray-400'
                      }`}></div>
                      <div>
                        <div className="font-medium text-gray-800">Status Updated</div>
                        <div className="text-sm text-gray-600">
                          Current status: {formatStatus(app.status)}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
        {/* Sidebar */}
        <aside className="w-full md:w-96 flex-shrink-0 flex flex-col gap-4">
          {/* User Profile Card */}
          <div className="bg-white rounded-xl shadow p-6">
            <div className="flex items-center gap-4 mb-4">
              {profile?.avatar ? (
                <img
                  src={profile.avatar}
                  alt={profile.name}
                  className="w-12 h-12 rounded-full object-cover"
                />
              ) : (
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                  <span className="text-blue-600 font-semibold text-lg">
                    {profile?.name?.charAt(0)?.toUpperCase() || 'U'}
                  </span>
                </div>
              )}
              <div className="flex-1">
                <div className="font-bold text-gray-800">{profile?.name || 'User'}</div>
                <div className="text-sm text-gray-500">{profile?.email || '<EMAIL>'}</div>
              </div>
            </div>

            <div className={`p-3 rounded-lg mb-4 ${getStatusColor(app.status)}`}>
              <div className="font-medium">Application Status</div>
              <div className="text-sm">{formatStatus(app.status)}</div>
            </div>

            <div className="flex gap-2 mb-4">
              <button className="flex-1 bg-gray-100 rounded-lg p-2 hover:bg-gray-200 transition text-center">
                <FaUser className="mx-auto" />
              </button>
              <button className="flex-1 bg-gray-100 rounded-lg p-2 hover:bg-gray-200 transition text-center">
                <FaShareAlt className="mx-auto" />
              </button>
            </div>

            <button
              disabled
              className="w-full bg-green-600 text-white font-bold py-3 rounded-lg cursor-not-allowed opacity-75"
            >
              <FaCheckCircle className="inline mr-2" />
              Application Submitted
            </button>
          </div>

          {/* Application Stats */}
          <div className="bg-white rounded-xl shadow p-6">
            <h3 className="font-bold text-lg mb-4 text-gray-800">Application Information</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Applied Date</span>
                <span className="font-medium text-gray-800">
                  {new Date(app.appliedAt).toLocaleDateString()}
                </span>
              </div>

              {app.job?.applicationDeadline && (
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Deadline</span>
                  <span className="font-medium text-gray-800">
                    {new Date(app.job.applicationDeadline).toLocaleDateString()}
                  </span>
                </div>
              )}

              {app.job?.currentApplications && (
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Total Applications</span>
                  <span className="font-medium text-gray-800">{app.job.currentApplications}</span>
                </div>
              )}

              {app.testScore && (
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Test Score</span>
                  <span className="font-medium text-green-600">{app.testScore}%</span>
                </div>
              )}
            </div>
          </div>
          {/* Eligibility Card */}
          <div className="bg-white rounded-xl shadow p-4">
            <div className="font-bold mb-2 text-[#23414c]">Eligibility</div>
            <div className="text-gray-700">{app.eligibility}</div>
          </div>
          {/* Refer & Win Card */}
          <div className="bg-white rounded-xl shadow p-4 flex flex-col gap-2">
            <div className="font-bold mb-1 text-[#23414c]">Refer & Win</div>
            <div className="text-xs text-gray-600">MacBook, iPhone, Apple Watch, Cash and more!</div>
            <button className="mt-2 px-4 py-2 rounded-full border border-[#23414c] text-[#23414c] font-semibold hover:bg-[#23414c]/10 transition">Refer now</button>
            <button className="text-xs text-[#23414c] hover:underline">Know more</button>
          </div>
          {/* Ad/Banner Card */}
          <div className="bg-white rounded-xl shadow p-4 flex flex-col items-center">
            <img src="https://assets9.lottiefiles.com/packages/lf20_3rwasyjy.json" alt="Unstop Pro" className="w-32 h-24 object-contain mb-2" />
            <div className="font-bold text-[#23414c] mb-1">Unstop Pro</div>
            <div className="text-xs text-gray-600 mb-2">50+ Courses | 15% Off Mentorship | 100+ Interview prep</div>
            <button className="bg-[#23414c] text-white px-4 py-2 rounded-full font-bold">Go Pro Now</button>
          </div>
        </aside>
      </div>
    </div>
  );
}

// Markdown renderer for description
function Markdown({ children }) {
  // Simple markdown to HTML for bullets and bold
  const html = children
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\n- /g, '<ul><li>')
    .replace(/\n/g, '<br/>')
    .replace(/<ul><li>(.*?)<br\/>/g, '<ul><li>$1</li>')
    .replace(/<br\/>/g, '')
    .replace(/<ul><li>/g, '<ul><li>')
    .replace(/<\/li><li>/g, '</li><li>')
    .replace(/<\/li><\/ul>/g, '</li></ul>');
  return <div dangerouslySetInnerHTML={{ __html: html }} />;
} 