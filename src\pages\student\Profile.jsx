import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import useStudentStore from '../../store/studentStore';
import { FaUserEdit, FaFileAlt, FaMedal, FaBriefcase, FaGraduationCap, FaTasks, FaCertificate, FaProjectDiagram, FaTrophy, FaPlus, FaEdit, FaShareAlt, FaLinkedin, FaGithub, FaTwitter, FaInstagram, FaFacebook, FaGlobe, FaEnvelope, FaStar, FaCamera, FaSpinner, FaExclamationTriangle } from 'react-icons/fa';

// Static profile data - replaced with dynamic data from API
const initialProfile = {
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  email: 'gabbisheik5297',
  address: 'Alva’s Institute of Engineering and Technology, Karnataka',
  avatar: '',
  about: '',
  resume: '',
  skills: [],
  work: [],
  education: [],
  responsibilities: [],
  certificates: [],
  projects: [],
  achievements: [],
  social: [
    { icon: <FaLinkedin />, url: '#' },
    { icon: <FaGithub />, url: '#' },
    { icon: <FaTwitter />, url: '#' },
    { icon: <FaInstagram />, url: '#' },
    { icon: <FaFacebook />, url: '#' },
    { icon: <FaGlobe />, url: '#' },
    { icon: <FaEnvelope />, url: '#' },
  ],
  streaks: [
    '2024-06-01', '2024-06-02', '2024-06-03', '2024-06-05', '2024-06-07',
    '2024-06-10', '2024-06-12', '2024-06-13', '2024-06-14', '2024-06-15',
  ],
};

const sectionKeys = [
  'about', 'resume', 'skills', 'work', 'education', 'responsibilities', 'certificates', 'projects', 'achievements'
];

const Profile = () => {
  const navigate = useNavigate();
  const {
    profile,
    profileLoading,
    profileError,
    updateProfile,
    saveProfile,
    fetchProfile,
    getProfileCompletion
  } = useStudentStore();

  const [editSection, setEditSection] = useState(null);
  const [temp, setTemp] = useState({});
  const [editHeader, setEditHeader] = useState(false);
  const [headerTemp, setHeaderTemp] = useState({
    name: profile.name,
    email: profile.email,
    address: profile.address,
    avatar: profile.avatar
  });
  const [dragActive, setDragActive] = useState(false);
  const [showSaved, setShowSaved] = useState(false);
  const fileInputRef = useRef();
  const [showProfileModal, setShowProfileModal] = useState(false);

  // Fetch profile on component mount and set up auto-refresh
  useEffect(() => {
    fetchProfile();

    // Auto-refresh profile every 5 minutes to keep data current
    const interval = setInterval(() => {
      fetchProfile();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [fetchProfile]);

  // Update headerTemp when profile changes
  useEffect(() => {
    setHeaderTemp({
      name: profile.name,
      email: profile.email,
      address: profile.address,
      avatar: profile.avatar
    });
  }, [profile]);

  // Use completion status from API, fallback to calculated completion
  const progress = profile.completionStatus || getProfileCompletion();

  // Handlers
  const handleEdit = (key) => {
    setEditSection(key);
    setTemp({ ...profile });
  };
  const handleCancel = () => setEditSection(null);
  const handleSave = async (key) => {
    try {
      updateProfile({ [key]: temp[key] });
      await saveProfile({ [key]: temp[key] });
      setEditSection(null);
      // Refresh profile data to ensure we have the latest from server
      await fetchProfile();
    } catch (error) {
      console.error('Failed to save profile:', error);
    }
  };
  const handleTempChange = (key, value) => setTemp({ ...temp, [key]: value });

  // Header edit handlers
  const handleHeaderEdit = () => {
    setHeaderTemp({ name: profile.name, email: profile.email, address: profile.address, avatar: profile.avatar });
    setEditHeader(true);
  };
  const handleHeaderCancel = () => setEditHeader(false);
  const handleHeaderSave = async () => {
    try {
      updateProfile(headerTemp);
      await saveProfile(headerTemp);
      setEditHeader(false);
      setShowSaved(true);
      setTimeout(() => setShowSaved(false), 1500);
      // Refresh profile data to ensure we have the latest from server
      await fetchProfile();
    } catch (error) {
      console.error('Failed to save profile header:', error);
    }
  };

  // Avatar upload handlers (update headerTemp.avatar if editing)
  const handleAvatarChange = (e) => {
    const file = e.target.files && e.target.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (ev) => {
        if (editHeader) {
          setHeaderTemp((prev) => ({ ...prev, avatar: ev.target.result }));
        } else {
          updateProfile({ avatar: ev.target.result });
        }
      };
      reader.readAsDataURL(file);
    }
  };
  const handleAvatarDrop = (e) => {
    e.preventDefault();
    setDragActive(false);
    const file = e.dataTransfer.files && e.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (ev) => {
        if (editHeader) {
          setHeaderTemp((prev) => ({ ...prev, avatar: ev.target.result }));
        } else {
          updateProfile({ avatar: ev.target.result });
        }
      };
      reader.readAsDataURL(file);
    }
  };
  const handleAvatarDragOver = (e) => {
    e.preventDefault();
    setDragActive(true);
  };
  const handleAvatarDragLeave = (e) => {
    e.preventDefault();
    setDragActive(false);
  };
  const handleAvatarClick = () => {
    fileInputRef.current.click();
  };

  // Loading state
  if (profileLoading) {
    return (
      <div className="min-h-screen bg-gray-50 w-full flex items-center justify-center">
        <div className="text-center">
          <FaSpinner className="animate-spin text-4xl text-blue-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Loading Profile...</h2>
          <p className="text-gray-500">Please wait while we fetch your profile data.</p>
        </div>
      </div>
    );
  }

  // Error state
  if (profileError) {
    return (
      <div className="min-h-screen bg-gray-50 w-full flex items-center justify-center">
        <div className="text-center max-w-md mx-auto">
          <FaExclamationTriangle className="text-4xl text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Error Loading Profile</h2>
          <p className="text-gray-500 mb-4">{profileError}</p>
          <button
            onClick={() => fetchProfile()}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 w-full">
      {/* Back Button */}
      <button
        className="ml-4 flex items-center gap-2 text-[#23414c] font-bold px-4 py-2 rounded hover:bg-[#23414c]/10 transition"
        onClick={() => navigate(-1)}
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" /></svg>
        Back
      </button>
      {/* Header */}
      <div className="w-full bg-gradient-to-r from-[#23414c] to-[#23414c] py-0 flex flex-col md:flex-row items-center md:items-center justify-between">
        <div className="flex items-center gap-8 flex-1 min-w-0">
          <div className="flex-shrink-0 relative">
            <div
              className={`w-24 h-24 rounded-full flex items-center justify-center text-5xl font-bold border-4 border-white shadow-lg cursor-pointer transition ${dragActive ? 'ring-4 ring-[#23414c]/50' : ''}`}
              style={{ background: (editHeader ? headerTemp.avatar : profile.avatar) ? `url(${editHeader ? headerTemp.avatar : profile.avatar}) center/cover no-repeat` : '#e5e7eb' }}
              onClick={handleAvatarClick}
              onDrop={handleAvatarDrop}
              onDragOver={handleAvatarDragOver}
              onDragLeave={handleAvatarDragLeave}
              title="Click or drag to upload photo"
            >
              {!(editHeader ? headerTemp.avatar : profile.avatar) && (
                <span className="text-gray-700">{profile.name.split(' ').map(n => n[0]).join('').toUpperCase()}</span>
              )}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleAvatarChange}
              />
            </div>
          </div>
          <div className="min-w-0">
            {editHeader ? (
              <div className="flex flex-col gap-2">
                <input
                  className="rounded px-3 py-2 text-lg font-bold"
                  value={headerTemp.name}
                  onChange={e => setHeaderTemp({ ...headerTemp, name: e.target.value })}
                  placeholder="Name"
                />
                <input
                  className="rounded px-3 py-2 text-base"
                  value={headerTemp.email}
                  onChange={e => setHeaderTemp({ ...headerTemp, email: e.target.value })}
                  placeholder="Email"
                />
                <input
                  className="rounded px-3 py-2 text-base"
                  value={headerTemp.address}
                  onChange={e => setHeaderTemp({ ...headerTemp, address: e.target.value })}
                  placeholder="Address"
                />
                <div className="flex gap-2 mt-2">
                  <button className="bg-[#23414c] text-white px-4 py-1 rounded" onClick={handleHeaderSave} disabled={JSON.stringify(headerTemp) === JSON.stringify({ name: profile.name, email: profile.email, address: profile.address, avatar: profile.avatar })}>Save</button>
                  <button className="bg-gray-100 text-[#23414c] px-4 py-1 rounded" onClick={handleHeaderCancel}>Cancel</button>
                </div>
                {showSaved && <div className="text-green-100 bg-green-600 px-3 py-1 rounded mt-2 text-center">Saved!</div>}
              </div>
            ) : (
              <>
                <div className="text-3xl font-extrabold text-white mb-1 truncate">{profile.name}</div>
                <div className="text-white/90 text-base mb-1 truncate">{profile.email}</div>
                <div className="text-white/90 text-base truncate">{profile.address}</div>
                <div className="flex items-center gap-3 mt-3">
                  <div className="w-64 h-3 bg-white/30 rounded-full overflow-hidden">
                    <div className="h-3 bg-green-400 rounded-full transition-all duration-300" style={{ width: `${progress}%` }}></div>
                  </div>
                  <span className="text-base text-white font-bold">{progress}%</span>
                </div>
              </>
            )}
          </div>
        </div>
        <button
          className="bg-white text-[#23414c] font-bold px-8 py-3 rounded-lg shadow hover:bg-white/80 flex items-center gap-2 text-lg"
          onClick={handleHeaderEdit}
          disabled={editHeader}
        >
          <FaUserEdit className="text-xl" /> Edit Profile
        </button>
      </div>
      {/* Main Content */}
      <div className="w-full flex flex-col md:flex-row gap-0 px-0">
        {/* Left/Main */}
        <div className="flex-1 flex flex-col gap-4">
          {/* About Section */}
          <ProfileSection
            icon={<FaUserEdit />} title="About"
            actionLabel={profile.about ? 'Edit About' : 'Add About'}
            onEdit={() => handleEdit('about')}
            isEditing={editSection === 'about'}
          >
            {editSection === 'about' ? (
              <div className="flex flex-col gap-2">
                <textarea
                  className="border rounded p-2 w-full"
                  rows={3}
                  value={temp.about || ''}
                  onChange={e => handleTempChange('about', e.target.value)}
                />
                <div className="flex gap-2 mt-2">
                  <button className="bg-[#23414c] text-white px-4 py-1 rounded" onClick={() => handleSave('about')}>Save</button>
                  <button className="bg-gray-100 text-[#23414c] px-4 py-1 rounded" onClick={handleCancel}>Cancel</button>
          </div>
          </div>
            ) : profile.about ? (
              <div className="text-gray-700 whitespace-pre-line">{profile.about}</div>
            ) : (
              <div className="text-gray-400">No about info added yet.</div>
            )}
          </ProfileSection>
          {/* Resume Section */}
          <ProfileSection
            icon={<FaFileAlt />} title="Resume"
            actionLabel={profile.resume ? 'Edit Resume' : 'Upload Resume'}
            isEditing={editSection === 'resume'}
            onEdit={() => handleEdit('resume')}
          >
            {editSection === 'resume' ? (
              <div className="flex flex-col gap-2">
                <input
                  type="text"
                  className="border rounded p-2 w-full"
                  placeholder="Paste resume link or description"
                  value={temp.resume || ''}
                  onChange={e => handleTempChange('resume', e.target.value)}
                />
                <input
                  type="file"
                  accept=".pdf,.doc,.docx,.txt"
                  className="border rounded p-2 w-full"
                  onChange={e => {
                    const file = e.target.files[0];
                    if (file) {
                      const reader = new FileReader();
                      reader.onload = (ev) => {
                        setTemp({ ...temp, resumeFile: { name: file.name, data: ev.target.result } });
                      };
                      reader.readAsDataURL(file);
                    }
                  }}
                />
                {temp.resumeFile && (
                  <div className="text-green-700 text-sm">Selected: {temp.resumeFile.name}</div>
                )}
                <div className="flex gap-2 mt-2">
                  <button className="bg-[#23414c] text-white px-4 py-1 rounded" onClick={async () => {
                    try {
                      const updates = temp.resumeFile
                        ? { resume: temp.resume, resumeFile: temp.resumeFile }
                        : { resume: temp.resume };
                      updateProfile(updates);
                      await saveProfile(updates);
                      setEditSection(null);
                    } catch (error) {
                      console.error('Failed to save resume:', error);
                    }
                  }}>Save</button>
                  <button className="bg-gray-100 text-[#23414c] px-4 py-1 rounded" onClick={handleCancel}>Cancel</button>
                </div>
              </div>
            ) : profile.resumeFile ? (
              <div className="flex flex-col gap-2">
                <a
                  href={profile.resumeFile.data}
                  download={profile.resumeFile.name}
                  className="text-blue-700 underline font-semibold"
                >
                  Download Resume: {profile.resumeFile.name}
                </a>
                {profile.resume && <div className="text-gray-700 break-all">{profile.resume}</div>}
              </div>
            ) : profile.resume ? (
              <div className="text-gray-700 break-all">{profile.resume}</div>
            ) : (
              <div className="text-gray-400">No resume uploaded yet.</div>
            )}
          </ProfileSection>
          {/* Skills Section */}
          <ProfileSection
            icon={<FaStar />} title="Skills"
            actionLabel={profile.skills.length ? 'Edit Skills' : 'Add Skills'}
            onEdit={() => handleEdit('skills')}
            isEditing={editSection === 'skills'}
          >
            {editSection === 'skills' ? (
              <div className="flex flex-col gap-2">
                <input
                  type="text"
                  className="border rounded p-2 w-full"
                  placeholder="Comma separated skills"
                  value={temp.skills ? temp.skills.join(', ') : ''}
                  onChange={e => handleTempChange('skills', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
                />
                <div className="flex gap-2 mt-2">
                  <button className="bg-[#23414c] text-white px-4 py-1 rounded" onClick={() => handleSave('skills')}>Save</button>
                  <button className="bg-gray-100 text-[#23414c] px-4 py-1 rounded" onClick={handleCancel}>Cancel</button>
            </div>
          </div>
            ) : profile.skills.length ? (
              <div className="flex flex-wrap gap-2">
                {profile.skills.map((skill, idx) => (
                  <span key={idx} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">{skill}</span>
                ))}
              </div>
            ) : (
              <div className="text-gray-400">No skills added yet.</div>
            )}
          </ProfileSection>
          {/* Work Experience Section */}
          <ProfileSection
            icon={<FaBriefcase />} title="Work Experience"
            actionLabel={profile.work.length ? 'Edit Work Experience' : 'Add Work Experience'}
            onEdit={() => handleEdit('work')}
            isEditing={editSection === 'work'}
          >
            {editSection === 'work' ? (
              <div className="flex flex-col gap-2">
                <textarea
                  className="border rounded p-2 w-full"
                  rows={3}
                  placeholder="Describe your work experience (one per line)"
                  value={temp.work ? temp.work.join('\n') : ''}
                  onChange={e => handleTempChange('work', e.target.value.split('\n').map(s => s.trim()).filter(Boolean))}
                />
                <div className="flex gap-2 mt-2">
                  <button className="bg-[#23414c] text-white px-4 py-1 rounded" onClick={() => handleSave('work')}>Save</button>
                  <button className="bg-gray-100 text-[#23414c] px-4 py-1 rounded" onClick={handleCancel}>Cancel</button>
                </div>
              </div>
            ) : profile.work.length ? (
              <ul className="list-disc ml-6 text-gray-700">
                {profile.work.map((item, idx) => <li key={idx}>{item}</li>)}
              </ul>
            ) : (
              <div className="text-gray-400">No work experience added yet.</div>
            )}
          </ProfileSection>
          {/* Education Section */}
          <ProfileSection
            icon={<FaGraduationCap />} title="Education"
            actionLabel={profile.education.length ? 'Edit Education' : 'Add Education'}
            onEdit={() => handleEdit('education')}
            isEditing={editSection === 'education'}
          >
            {editSection === 'education' ? (
              <div className="flex flex-col gap-2">
                <textarea
                  className="border rounded p-2 w-full"
                  rows={3}
                  placeholder="Describe your education (one per line)"
                  value={temp.education ? temp.education.join('\n') : ''}
                  onChange={e => handleTempChange('education', e.target.value.split('\n').map(s => s.trim()).filter(Boolean))}
                />
                <div className="flex gap-2 mt-2">
                  <button className="bg-[#23414c] text-white px-4 py-1 rounded" onClick={() => handleSave('education')}>Save</button>
                  <button className="bg-gray-100 text-[#23414c] px-4 py-1 rounded" onClick={handleCancel}>Cancel</button>
                </div>
              </div>
            ) : profile.education.length ? (
              <ul className="list-disc ml-6 text-gray-700">
                {profile.education.map((item, idx) => <li key={idx}>{item}</li>)}
              </ul>
            ) : (
              <div className="text-gray-400">No education added yet.</div>
            )}
          </ProfileSection>
          {/* Responsibilities Section */}
          <ProfileSection
            icon={<FaTasks />} title="Responsibilities"
            actionLabel={profile.responsibilities.length ? 'Edit Responsibility' : 'Add Responsibility'}
            onEdit={() => handleEdit('responsibilities')}
            isEditing={editSection === 'responsibilities'}
          >
            {editSection === 'responsibilities' ? (
              <div className="flex flex-col gap-2">
                <textarea
                  className="border rounded p-2 w-full"
                  rows={3}
                  placeholder="Describe your responsibilities (one per line)"
                  value={temp.responsibilities ? temp.responsibilities.join('\n') : ''}
                  onChange={e => handleTempChange('responsibilities', e.target.value.split('\n').map(s => s.trim()).filter(Boolean))}
                />
                <div className="flex gap-2 mt-2">
                  <button className="bg-[#23414c] text-white px-4 py-1 rounded" onClick={() => handleSave('responsibilities')}>Save</button>
                  <button className="bg-gray-100 text-[#23414c] px-4 py-1 rounded" onClick={handleCancel}>Cancel</button>
                </div>
              </div>
            ) : profile.responsibilities.length ? (
              <ul className="list-disc ml-6 text-gray-700">
                {profile.responsibilities.map((item, idx) => <li key={idx}>{item}</li>)}
              </ul>
            ) : (
              <div className="text-gray-400">No responsibilities added yet.</div>
            )}
          </ProfileSection>
          {/* Certificates Section */}
          <ProfileSection
            icon={<FaCertificate />} title="Certificates"
            actionLabel={profile.certificates.length ? 'Edit Certificate' : 'Add Certificate'}
            onEdit={() => handleEdit('certificates')}
            isEditing={editSection === 'certificates'}
          >
            {editSection === 'certificates' ? (
              <div className="flex flex-col gap-2">
                <textarea
                  className="border rounded p-2 w-full"
                  rows={3}
                  placeholder="Describe your certificates (one per line)"
                  value={temp.certificates ? temp.certificates.join('\n') : ''}
                  onChange={e => handleTempChange('certificates', e.target.value.split('\n').map(s => s.trim()).filter(Boolean))}
                />
                <div className="flex gap-2 mt-2">
                  <button className="bg-[#23414c] text-white px-4 py-1 rounded" onClick={() => handleSave('certificates')}>Save</button>
                  <button className="bg-gray-100 text-[#23414c] px-4 py-1 rounded" onClick={handleCancel}>Cancel</button>
                </div>
              </div>
            ) : profile.certificates.length ? (
              <ul className="list-disc ml-6 text-gray-700">
                {profile.certificates.map((item, idx) => <li key={idx}>{item}</li>)}
              </ul>
            ) : (
              <div className="text-gray-400">No certificates added yet.</div>
            )}
          </ProfileSection>
          {/* Projects Section */}
          <ProfileSection
            icon={<FaProjectDiagram />} title="Projects"
            actionLabel={profile.projects.length ? 'Edit Project' : 'Add Project'}
            onEdit={() => handleEdit('projects')}
            isEditing={editSection === 'projects'}
          >
            {editSection === 'projects' ? (
              <div className="flex flex-col gap-2">
                <textarea
                  className="border rounded p-2 w-full"
                  rows={3}
                  placeholder="Describe your projects (one per line)"
                  value={temp.projects ? temp.projects.join('\n') : ''}
                  onChange={e => handleTempChange('projects', e.target.value.split('\n').map(s => s.trim()).filter(Boolean))}
                />
                <div className="flex gap-2 mt-2">
                  <button className="bg-[#23414c] text-white px-4 py-1 rounded" onClick={() => handleSave('projects')}>Save</button>
                  <button className="bg-gray-100 text-[#23414c] px-4 py-1 rounded" onClick={handleCancel}>Cancel</button>
                </div>
          </div>
            ) : profile.projects.length ? (
              <ul className="list-disc ml-6 text-gray-700">
                {profile.projects.map((item, idx) => <li key={idx}>{item}</li>)}
              </ul>
            ) : (
              <div className="text-gray-400">No projects added yet.</div>
            )}
          </ProfileSection>
          {/* Achievements Section */}
          <ProfileSection
            icon={<FaTrophy />} title="Achievements"
            actionLabel={profile.achievements.length ? 'Edit Achievement' : 'Add Achievement'}
            onEdit={() => handleEdit('achievements')}
            isEditing={editSection === 'achievements'}
          >
            {editSection === 'achievements' ? (
              <div className="flex flex-col gap-2">
                <textarea
                  className="border rounded p-2 w-full"
                  rows={3}
                  placeholder="Describe your achievements (one per line)"
                  value={temp.achievements ? temp.achievements.join('\n') : ''}
                  onChange={e => handleTempChange('achievements', e.target.value.split('\n').map(s => s.trim()).filter(Boolean))}
                />
                <div className="flex gap-2 mt-2">
                  <button className="bg-[#23414c] text-white px-4 py-1 rounded" onClick={() => handleSave('achievements')}>Save</button>
                  <button className="bg-gray-100 text-[#23414c] px-4 py-1 rounded" onClick={handleCancel}>Cancel</button>
                  </div>
              </div>
            ) : profile.achievements.length ? (
              <ul className="list-disc ml-6 text-gray-700">
                {profile.achievements.map((item, idx) => <li key={idx}>{item}</li>)}
              </ul>
            ) : (
              <div className="text-gray-400">No achievements added yet.</div>
            )}
          </ProfileSection>
          {/* Social Links */}
          <div className="bg-white rounded-xl shadow p-4 flex flex-col gap-2">
            <div className="font-bold mb-2">Social Links</div>
            <div className="flex gap-2 flex-wrap mb-2">
              {profile.social.map((s, i) => (
                <a key={i} href={s.url} className="text-gray-500 hover:text-[#23414c] text-xl" target="_blank" rel="noopener noreferrer">{s.icon}</a>
              ))}
            </div>
            {/* Add/Edit Social Links */}
            <div className="flex flex-col gap-2">
              <div className="flex gap-2">
                <select
                  className="border rounded px-2 py-1"
                  value={temp.socialIcon || ''}
                  onChange={e => setTemp({ ...temp, socialIcon: e.target.value })}
                >
                  <option value="">Select Icon</option>
                  <option value="linkedin">LinkedIn</option>
                  <option value="github">GitHub</option>
                  <option value="twitter">Twitter</option>
                  <option value="instagram">Instagram</option>
                  <option value="facebook">Facebook</option>
                  <option value="globe">Website</option>
                  <option value="envelope">Email</option>
                </select>
                <input
                  className="border rounded px-2 py-1 flex-1"
                  placeholder="Paste URL"
                  value={temp.socialUrl || ''}
                  onChange={e => setTemp({ ...temp, socialUrl: e.target.value })}
                />
                <button
                  className="bg-[#23414c] text-white px-3 py-1 rounded"
                  onClick={() => {
                    if (temp.socialIcon && temp.socialUrl) {
                      const iconMap = {
                        linkedin: <FaLinkedin />,
                        github: <FaGithub />,
                        twitter: <FaTwitter />,
                        instagram: <FaInstagram />,
                        facebook: <FaFacebook />,
                        globe: <FaGlobe />,
                        envelope: <FaEnvelope />,
                      };
                      updateProfile({
                        social: [
                          ...profile.social,
                          { icon: iconMap[temp.socialIcon], url: temp.socialUrl },
                        ],
                      });
                      setTemp({ ...temp, socialIcon: '', socialUrl: '' });
                    }
                  }}
                >
                  Add
                </button>
              </div>
              <div className="flex flex-col gap-1 mt-2">
                {profile.social.map((s, i) => (
                  <div key={i} className="flex items-center gap-2 text-sm">
                    <span className="text-xl">{s.icon}</span>
                    <span className="truncate max-w-xs">{s.url}</span>
                    <button
                      className="text-red-500 text-xs ml-2"
                      onClick={() => updateProfile({
                        social: profile.social.filter((_, idx) => idx !== i),
                      })}
                    >
                      Remove
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
          {/* Streaks */}
          {/* Streaks section removed */}
        </div>
        {/* Right/Sidebar */}
        <div className="w-full md:w-80 flex-shrink-0 flex flex-col gap-4">
          {/* Purpose Card */}
          <div className="bg-white rounded-xl shadow p-4 flex flex-col gap-2">
            <div className="font-bold mb-2">What's your purpose?</div>
            <div className="text-xs text-gray-600 mb-2">Select your intention on Unstop, tell us what you're here to achieve.</div>
            <div className="flex flex-col gap-2">
              <button className="border rounded px-3 py-2 text-left flex items-center gap-2 hover:bg-[#23414c]/10 text-[#23414c] border-[#23414c]" onClick={() => navigate('/student/jobs')}><FaStar /> To find a job</button>
            </div>
            <div className="flex gap-2 mt-2"></div>
          </div>
          {/* Save and View Profile Buttons */}
          <div className="flex flex-col items-start gap-4">
            <button
              className="bg-[#23414c] text-white px-8 py-3 rounded-lg shadow font-bold text-lg hover:bg-[#23414c]/90 transition"
              onClick={() => {
                alert('Profile saved!');
              }}
            >
              Save Profile
            </button>
            <button
              className="bg-white border border-[#23414c] text-[#23414c] px-8 py-3 rounded-lg shadow font-bold text-lg hover:bg-[#23414c]/10 transition"
              onClick={() => setShowProfileModal(true)}
            >
              View Profile
            </button>
          </div>
          {/* Rankings Card REMOVED */}
        </div>
      </div>
      {/* Global Save Button */}
      {/* Profile Modal */}
      {showProfileModal && (
        <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-2xl p-8 max-w-2xl w-full relative">
            <button
              className="absolute top-2 right-2 text-2xl text-gray-400 hover:text-[#23414c]"
              onClick={() => setShowProfileModal(false)}
              title="Close"
            >
              &times;
            </button>
            <h2 className="text-2xl font-bold mb-4 text-[#23414c]">Saved Profile</h2>
            <div className="flex flex-col gap-2">
              <div><span className="font-bold">Name:</span> {profile.name}</div>
              <div><span className="font-bold">Email:</span> {profile.email}</div>
              <div><span className="font-bold">Address:</span> {profile.address}</div>
              {profile.resumeFile && (
                <div><span className="font-bold">Resume:</span> <a href={profile.resumeFile.data} download={profile.resumeFile.name} className="text-blue-700 underline">{profile.resumeFile.name}</a></div>
              )}
              {profile.resume && <div><span className="font-bold">Resume Link/Desc:</span> {profile.resume}</div>}
              <div><span className="font-bold">About:</span> {profile.about}</div>
              <div><span className="font-bold">Skills:</span> {Array.isArray(profile.skills) ? profile.skills.join(', ') : profile.skills}</div>
              <div><span className="font-bold">Work:</span> {Array.isArray(profile.work) ? profile.work.join('; ') : profile.work}</div>
              <div><span className="font-bold">Education:</span> {Array.isArray(profile.education) ? profile.education.join('; ') : profile.education}</div>
              <div><span className="font-bold">Responsibilities:</span> {Array.isArray(profile.responsibilities) ? profile.responsibilities.join('; ') : profile.responsibilities}</div>
              <div><span className="font-bold">Certificates:</span> {Array.isArray(profile.certificates) ? profile.certificates.join('; ') : profile.certificates}</div>
              <div><span className="font-bold">Projects:</span> {Array.isArray(profile.projects) ? profile.projects.join('; ') : profile.projects}</div>
              <div><span className="font-bold">Achievements:</span> {Array.isArray(profile.achievements) ? profile.achievements.join('; ') : profile.achievements}</div>
              <div><span className="font-bold">Social Links:</span> {profile.social && profile.social.map((s, i) => <a key={i} href={s.url} className="text-blue-700 underline ml-2" target="_blank" rel="noopener noreferrer">{s.url}</a>)}</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

function ProfileSection({ icon, title, actionLabel, onEdit, children }) {
  return (
    <div className="bg-white rounded-xl shadow p-4 flex flex-col gap-2">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2 font-bold text-gray-800 text-lg">
          <span className="text-[#23414c] text-xl">{icon}</span>
          {title}
        </div>
        <button className="flex items-center gap-1 text-[#23414c] hover:underline text-sm font-semibold" onClick={onEdit}><FaPlus /> {actionLabel}</button>
      </div>
      {children}
    </div>
  );
}

export default Profile;