import React, { useEffect, useState } from "react";
import { useAuth0 } from "@auth0/auth0-react";

// Helper to get default resume fields
const getInitialResume = (userData) => ({
  name: userData?.name || "",
  email: userData?.email || "",
  summary: userData?.about || "",
  skills: userData?.customFields?.skills || "",
  education: userData?.customFields?.education || "",
  experience: userData?.customFields?.experience || "",
  // Add more fields as needed, mapping from customFields or userData
});

const RESUME_BUILDER_URL = "https://resumebuilder.proyuj.com";

const Resume = () => {
  const { getAccessTokenSilently } = useAuth0();
  const [loading, setLoading] = useState(true);
  const [resume, setResume] = useState({
    name: "",
    email: "",
    summary: "",
    skills: "",
    education: "",
    experience: "",
  });
  const [status, setStatus] = useState("");
  const [error, setError] = useState("");

  // Fetch user profile for resume pre-fill
  useEffect(() => {
    (async () => {
      setLoading(true);
      setStatus("");
      setError("");
      try {
        const token = await getAccessTokenSilently({
          audience: "https://interviewbackend-zfwp.onrender.com/api",
          scope: "openid profile email",
        });
        const res = await fetch(
          "https://interviewbackend-zfwp.onrender.com/api/user/profile",
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (!res.ok) throw new Error("Failed to load profile");
        const data = await res.json();
        setResume(getInitialResume(data.user));
      } catch (err) {
        setError("Could not fetch user profile for resume");
      } finally {
        setLoading(false);
      }
    })();
    // eslint-disable-next-line
  }, []);

  // Submit resume (PUT)
  const handleSubmit = async (e) => {
    e.preventDefault();
    setStatus("Saving...");
    setError("");
    try {
      const token = await getAccessTokenSilently({
        audience: "https://interviewbackend-zfwp.onrender.com/api",
        scope: "openid profile email",
      });

      // Prepare profile update payload
      const payload = {
        name: resume.name,
        email: resume.email,
        about: resume.summary,
        customFields: {
          skills: resume.skills,
          education: resume.education,
          experience: resume.experience,
        },
      };

      const res = await fetch(
        "https://interviewbackend-zfwp.onrender.com/api/user/profile",
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(payload),
        }
      );

      if (!res.ok) throw new Error("Failed to update resume");
      setStatus("Resume saved!");
    } catch (err) {
      setError("Error saving resume");
      setStatus("");
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto py-8 flex flex-col gap-8">
      <div className="bg-gradient-to-br from-blue-50 via-white to-blue-100 border border-blue-200 rounded-3xl shadow-2xl mb-4 p-8 relative flex flex-col items-center">
        
        <h2 className="text-4xl font-extrabold mb-2 text-blue-900 text-center tracking-tight">
          Resume Builder
        </h2>
        <p className="text-gray-600 text-center mb-4">
          Quickly craft your resume below, or use our{" "}
          <a
            href={RESUME_BUILDER_URL}
            className="text-blue-700 underline font-medium hover:text-blue-900"
            target="_blank"
            rel="noopener noreferrer"
          >
            AI-powered Resume Builder
          </a>{" "}
          for a smarter experience!
        </p>
        {loading ? (
          <div className="text-blue-700 font-semibold text-center py-8">
            Loading profile...
          </div>
        ) : (
          <form className="w-full space-y-5" onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block mb-2 font-semibold text-gray-700">
                  Name
                </label>
                <input
                  type="text"
                  className="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-400 bg-gray-50"
                  value={resume.name}
                  onChange={(e) =>
                    setResume((prev) => ({ ...prev, name: e.target.value }))
                  }
                  required
                  placeholder="Your full name"
                />
              </div>
              <div>
                <label className="block mb-2 font-semibold text-gray-700">
                  Email
                </label>
                <input
                  type="email"
                  className="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-400 bg-gray-50"
                  value={resume.email}
                  onChange={(e) =>
                    setResume((prev) => ({ ...prev, email: e.target.value }))
                  }
                  required
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
            <div>
              <label className="block mb-2 font-semibold text-gray-700">
                Summary
              </label>
              <textarea
                className="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-400 bg-gray-50"
                rows={2}
                value={resume.summary}
                onChange={(e) =>
                  setResume((prev) => ({
                    ...prev,
                    summary: e.target.value,
                  }))
                }
                placeholder="A short professional summary"
              />
            </div>
            <div>
              <label className="block mb-2 font-semibold text-gray-700">
                Skills
              </label>
              <input
                type="text"
                className="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-400 bg-gray-50"
                value={resume.skills}
                onChange={(e) =>
                  setResume((prev) => ({ ...prev, skills: e.target.value }))
                }
                placeholder="e.g. JavaScript, React, Node.js"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block mb-2 font-semibold text-gray-700">
                  Education
                </label>
                <input
                  type="text"
                  className="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-400 bg-gray-50"
                  value={resume.education}
                  onChange={(e) =>
                    setResume((prev) => ({
                      ...prev,
                      education: e.target.value,
                    }))
                  }
                  placeholder="Your degree & institution"
                />
              </div>
              <div>
                <label className="block mb-2 font-semibold text-gray-700">
                  Experience
                </label>
                <textarea
                  className="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-400 bg-gray-50"
                  rows={2}
                  value={resume.experience}
                  onChange={(e) =>
                    setResume((prev) => ({
                      ...prev,
                      experience: e.target.value,
                    }))
                  }
                  placeholder="Work experience"
                />
              </div>
            </div>
            <div className="flex justify-center pt-4">
              <button
                type="submit"
                className="bg-blue-600 text-white px-8 py-3 rounded-lg shadow hover:bg-blue-700 font-semibold text-lg transition"
                disabled={!!status && status === "Saving..."}
              >
                {status === "Saving..." ? "Saving..." : "Save Resume"}
              </button>
            </div>
            {status && status !== "Saving..." && (
              <div className="text-green-600 text-center font-semibold mt-2">
                {status}
              </div>
            )}
            {error && (
              <div className="text-red-600 text-center font-semibold mt-2">
                {error}
              </div>
            )}
          </form>
        )}
      </div>
    </div>
  );
};

export default Resume;
