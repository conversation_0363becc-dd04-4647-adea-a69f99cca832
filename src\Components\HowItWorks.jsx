import React, { useRef, useEffect, useState } from "react";
import { motion, useAnimation } from "framer-motion";
import registicon from "../assets/WITregistration.png";
import logingIcon from "../assets/login.png";
import schedule from "../assets/schedule.png";
import onlineTest from "../assets/onlinetest.png";
import completed from '../assets/completed.png'

// Data for left and right columns, matching your image structure
const steps = [
  // Row 1
  [
    {
      label: "Registration",
      desc: "Create your account and set up your profile.",
      icon: registicon,
      iconType: "img",
    },
    {
      label: "Login",
      desc: "Login securely to your dashboard.",
      icon: logingIcon,
      iconType: "img",
    },
  ],
  // Row 2
  [
    {
      label: "Aptitude Test Path",
      desc: (
        <ul className="text-base list-disc list-inside mt-2 space-y-0.5 font-normal text-left text-[#23263a]">
          <li>Choose Domain</li>
          <li>Choose Field</li>
          <li>Select Difficulty</li>
          <li>Attempt Questions</li>
        </ul>
      ),
      icon: onlineTest,
      iconType: "img",
    },
    {
      label: "Interview Prep Path",
      desc: (
        <ul className="text-base list-disc list-inside mt-2 space-y-0.5 font-normal text-left text-[#23263a]">
          <li>Go to Dashboard</li>
          <li>Open Schedule Page</li>
          <li>Choose Interviewer</li>
          <li>Schedule & Join Interview</li>
        </ul>
      ),
      icon: schedule,
      iconType: "img",
    },
  ],
  // Row 3
  [
    {
      label: "Feedback",
      desc: "Receive feedback and improve your skills.",
      iconType: "icon",
      icon: (
        <svg
          width="56"
          height="56"
          fill="none"
          stroke="#23263a"
          strokeWidth="2"
        >
          <circle cx="28" cy="28" r="26" fill="none" />
          <circle cx="28" cy="28" r="18" fill="none" />
          <text x="28" y="34" textAnchor="middle" fontSize="18" fill="#23263a">
            💬
          </text>
        </svg>
      ),
    },
    {
      label: "You're Ready!",
      desc: "Celebrate your progress and get interview-ready!",
      iconType: "car",
      icon: completed,
    },
  ],
];

// Car SVG (animated)
const AnimatedCar = ({ trigger }) => {
  // Use framer-motion key to re-trigger car animation
  return (
    <motion.div
      style={{ width: "90px", margin: "0 auto" }}
      initial={{ x: 100, rotate: 15, scale: 0.92, opacity: 0 }}
      animate={
        trigger ? { x: 0, rotate: [15, -10, 5, 0], scale: 1, opacity: 1 } : {}
      }
      transition={{
        x: { type: "spring", stiffness: 70, damping: 12, duration: 1.2 },
        rotate: { type: "spring", stiffness: 120, damping: 10, duration: 0.9 },
        scale: { duration: 0.5 },
        opacity: { duration: 0.5 },
      }}
    >
      <motion.svg
        width="90"
        height="54"
        viewBox="0 0 90 54"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <ellipse
          cx="24"
          cy="47"
          rx="7"
          ry="7"
          fill="#fff"
          stroke="#23263a"
          strokeWidth="3"
        />
        <ellipse
          cx="66"
          cy="47"
          rx="7"
          ry="7"
          fill="#fff"
          stroke="#23263a"
          strokeWidth="3"
        />
        <rect
          x="12"
          y="20"
          width="66"
          height="18"
          rx="7"
          fill="#fff"
          stroke="#23263a"
          strokeWidth="3"
        />
        <rect
          x="32"
          y="8"
          width="26"
          height="18"
          rx="7"
          fill="#fff"
          stroke="#23263a"
          strokeWidth="3"
        />
        <rect x="36" y="13" width="10" height="6" rx="2" fill="#e5e7eb" />
        <rect x="48" y="13" width="10" height="6" rx="2" fill="#e5e7eb" />
        <ellipse
          cx="45"
          cy="50"
          rx="23"
          ry="2"
          fill="#222"
          fillOpacity="0.17"
        />
        <circle cx="62" cy="34" r="2" fill="#23263a" />
        <circle cx="28" cy="34" r="2" fill="#23263a" />
        <rect
          x="13"
          y="38"
          width="64"
          height="3"
          fill="#23263a"
          fillOpacity="0.08"
        />
      </motion.svg>
      {/* Car sparkle */}
      <motion.div
        className="absolute"
        style={{ left: 58, top: 11 }}
        initial={{ opacity: 0, scale: 0.6 }}
        animate={trigger ? { opacity: [0, 1, 0], scale: [0.6, 1.2, 0.6] } : {}}
        transition={{ duration: 1.2, delay: 0.3 }}
      >
        <svg width="24" height="24" fill="none">
          <circle cx="12" cy="12" r="8" fill="#3069f1" opacity="0.22" />
        </svg>
      </motion.div>
    </motion.div>
  );
};

const variantsBox = {
  hidden: (custom) => ({
    opacity: 0,
    x: custom === "left" ? -80 : 80,
    scale: 0.95,
    rotate: custom === "left" ? -8 : 8,
    filter: "blur(4px)",
  }),
  visible: (custom) => ({
    opacity: 1,
    x: 0,
    scale: 1,
    rotate: 0,
    filter: "blur(0px)",
    transition: {
      x: { type: "spring", stiffness: 80, damping: 14 },
      scale: { type: "spring", stiffness: 150, damping: 20 },
      rotate: { duration: 0.55 },
      opacity: { duration: 0.36 },
      filter: { duration: 0.43 },
      delay: custom === "left" ? 0.07 : 0.22, // slight stagger
    },
  }),
};

const HowItWorks = () => {
  // Animation triggers for each row
  const [activeRow, setActiveRow] = useState(0);
  const containerRef = useRef();
  const [carTrigger, setCarTrigger] = useState(false);

  useEffect(() => {
    // Track which row is most in view for blue dot highlight
    const handleScroll = () => {
      const rows = document.querySelectorAll(".hiw-row");
      let found = 0;
      for (let i = 0; i < rows.length; i++) {
        const rect = rows[i].getBoundingClientRect();
        if (
          rect.top < window.innerHeight * 0.44 &&
          rect.bottom > window.innerHeight * 0.2
        ) {
          found = i;
        }
      }
      setActiveRow(found);
      // Only trigger car animation if last row is active
      setCarTrigger(found === steps.length - 1);
    };
    window.addEventListener("scroll", handleScroll, { passive: true });
    handleScroll();
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <section
      className="relative py-10 md:py-24 min-h-screen flex flex-col items-center bg-[#fcfcfc]"
      id="HowItWorks"
      ref={containerRef}
    >
      <motion.h2
        className="text-3xl md:text-5xl font-black text-[#23263a] mb-10 md:mb-16 text-center tracking-tight"
        initial={{ scale: 0.92, opacity: 0 }}
        whileInView={{ scale: 1, opacity: 1 }}
        viewport={{ once: false, amount: 0.75 }}
        transition={{ duration: 0.75 }}
      >
        How It Works
      </motion.h2>
      <div className="relative flex flex-row w-full max-w-5xl mx-auto">
        {/* Timeline & Dots */}
        <div
          className="absolute left-1/2 -translate-x-1/2 h-full flex flex-col items-center z-0 pointer-events-none"
          style={{
            width: "32px",
          }}
        >
          {/* Vertical Line */}
          <div
            className="absolute top-0 left-1/2 -translate-x-1/2 w-1 bg-[#23263a] h-full"
            style={{ minHeight: 420 }}
          />
          {/* Blue Dots */}
          {steps.map((row, idx) => (
            <div
              key={idx}
              className="relative"
              style={{
                top: `${idx * 33 + 5}%`,
                marginTop: idx === 0 ? "0" : "-16px",
                zIndex: 3,
              }}
            >
              <span
                className={`block mx-auto rounded-full border-2 border-[#23263a] bg-white transition-all duration-300
                  ${activeRow === idx ? "bg-[#3069f1] border-[#3069f1]" : ""}
                `}
                style={{
                  width: 20,
                  height: 20,
                  boxShadow: activeRow === idx ? "0 0 0 4px #3069f115" : "none",
                  background: activeRow === idx ? "#3069f1" : "#fff",
                  borderColor: activeRow === idx ? "#3069f1" : "#23263a",
                }}
              ></span>
            </div>
          ))}
        </div>
        {/* Steps Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-y-16 gap-x-8 w-full z-10">
          {steps.map((row, rIdx) =>
            row.map((step, cIdx) => (
              <motion.div
                key={rIdx + "_" + cIdx}
                className="hiw-row group flex flex-col items-center justify-center px-2"
                custom={cIdx === 0 ? "left" : "right"}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: false, amount: 0.33 }}
                variants={variantsBox}
                transition={{
                  duration: 0.7,
                  delay: rIdx * 0.28 + cIdx * 0.14,
                }}
              >
                <div
                  className={`w-full bg-white rounded-2xl border-2 border-[#23263a] p-6 sm:p-8 flex flex-col items-center shadow-md
                  ${cIdx === 0 ? "mr-auto md:ml-0" : "ml-auto md:mr-0"}
                  ${step.iconType === "car" ? "shadow-lg" : ""}
                `}
                  style={{
                    minWidth: 260,
                    maxWidth: 360,
                    minHeight: 176,
                    position: "relative",
                    boxShadow:
                      step.iconType === "car"
                        ? "0 6px 32px 0 #23263a12, 0 1.5px 6px 0 #23263a30"
                        : "0 2px 10px 0 #23263a09",
                  }}
                >
                  {/* Icon */}
                  <div className="mb-4">
                    {step.iconType === "img" && (
                      <motion.img
                        src={step.icon}
                        alt={step.label}
                        className="w-16 h-16 object-contain mx-auto"
                        loading="lazy"
                        initial={{ scale: 0.88, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ duration: 0.45, delay: 0.12 }}
                      />
                    )}
                    {step.iconType === "icon" && (
                      <motion.div
                        initial={{ scale: 0.85, opacity: 0 }}
                        animate={{ scale: 1.04, opacity: 1 }}
                        transition={{ duration: 0.5, delay: 0.13 }}
                      >
                        {step.icon}
                      </motion.div>
                    )}
                    {step.iconType === "car" && (
                      <AnimatedCar trigger={carTrigger} />
                    )}
                  </div>
                  <div
                    className={`flex flex-col ${
                      cIdx === 0
                        ? "items-start text-left"
                        : "items-end text-right"
                    } w-full`}
                  >
                    <motion.h4
                      className="font-extrabold text-lg md:text-xl text-[#23263a] mb-2"
                      initial={{ y: 10, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ duration: 0.42, delay: 0.11 }}
                    >
                      {step.label}
                    </motion.h4>
                    <motion.div
                      className="text-[#23263a] text-base font-normal w-full"
                      initial={{ y: 18, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ duration: 0.46, delay: 0.2 }}
                    >
                      {step.desc}
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
