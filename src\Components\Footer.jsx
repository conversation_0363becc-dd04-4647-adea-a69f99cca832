import {
  Fa<PERSON><PERSON><PERSON><PERSON>,
  Fa<PERSON>inkedinIn,
  <PERSON>a<PERSON><PERSON><PERSON>ram,
  FaTwitter,
} from "react-icons/fa";

const Footer = () => (
  <footer className="bg-[#29354d] text-white py-8 px-4 w-full relative overflow-hidden">
    {/* Decorative Glowing Circles */}
    <div className="pointer-events-none absolute -top-32 -left-32 w-80 h-80 rounded-full bg-[#FCA311]/20 blur-3xl opacity-60 z-0" />
    <div className="pointer-events-none absolute bottom-0 right-0 w-60 h-60 rounded-full bg-[#61DAFB]/10 blur-2xl opacity-50 z-0" />

    <div className="relative z-10 max-w-7xl mx-auto flex flex-col gap-10 md:gap-8 md:flex-row md:justify-between md:items-start">
      {/* Logo & Tagline */}
      <div className="flex-1 min-w-[210px] flex flex-col gap-4 items-center md:items-start mb-8 md:mb-0">
        <div className="flex items-center gap-2">
          {/* You can replace with your SVG or logo image */}
          <div className="w-10 h-10 bg-[#FCA311] rounded-full flex items-center justify-center text-[#29354d] font-black text-2xl select-none">
            P
          </div>
          <span className="text-2xl font-bold text-white tracking-tight">
            Proyuj
          </span>
        </div>
        <p className="text-sm text-gray-300 max-w-xs leading-relaxed mt-2 text-center md:text-left">
          Accelerate your interview prep and job journey with Proyuj’s
          interactive platform, tailored mock interviews, and domain-specific
          tests.
        </p>
      </div>

      {/* Quick Links */}
      <div className="flex-1 min-w-[180px] flex flex-col items-center md:items-start mb-8 md:mb-0">
        <h4 className="text-white text-lg font-bold mb-4 tracking-wide">
          Quick Links
        </h4>
        <ul className="space-y-2 font-medium text-center md:text-left">
          {["Home", "About Us", "Services", "Testimonials", "Contact"].map(
            (item) => (
              <li key={item}>
                <a
                  href={`#${item.replace(/\s+/g, "")}`}
                  className="hover:text-[#FCA311] transition-colors duration-200"
                >
                  {item}
                </a>
              </li>
            )
          )}
        </ul>
      </div>

      {/* Contact Info */}
      <div className="flex-1 min-w-[220px] flex flex-col items-center md:items-start mb-8 md:mb-0">
        <h4 className="text-white text-lg font-bold mb-4 tracking-wide">
          Contact Us
        </h4>
        <ul className="space-y-2 text-sm font-medium text-center md:text-left">
          <li className="flex flex-col items-center md:flex-row md:items-start gap-1 md:gap-2">
            <span className="text-[#FCA311] min-w-[52px]">Email:</span>
            <a
              href="mailto:<EMAIL>"
              className="hover:text-[#FCA311] break-all transition-colors duration-200"
            >
              <EMAIL>
            </a>
          </li>
          <li className="flex flex-col items-center md:flex-row md:items-start gap-1 md:gap-2">
            <span className="text-[#FCA311] min-w-[52px]">Phone:</span>
            <a
              href="tel:+91XXXXXXXXXX"
              className="hover:text-[#FCA311] transition-colors duration-200"
            >
              +91 XXXXX XXXXX
            </a>
          </li>
          <li className="flex flex-col items-center md:flex-row md:items-start gap-1 md:gap-2">
            <span className="text-[#FCA311] min-w-[52px]">Address:</span>
            <span className="text-gray-300">
              123, Startup Street, Bangalore, India
            </span>
          </li>
        </ul>
      </div>

      {/* Social Media */}
      <div className="flex-1 min-w-[190px] flex flex-col items-center md:items-start">
        <h4 className="text-white text-lg font-bold mb-4 tracking-wide">
          Follow Us
        </h4>
        <div className="flex space-x-4 mb-3">
          <a
            href="#"
            aria-label="Facebook"
            className="group rounded-full border border-[#FCA311]/30 p-2 hover:bg-[#FCA311] hover:text-[#29354d] transition-all duration-200 shadow-md shadow-[#FCA311]/20"
          >
            <FaFacebookF size={18} />
          </a>
          <a
            href="#"
            aria-label="LinkedIn"
            className="group rounded-full border border-[#FCA311]/30 p-2 hover:bg-[#FCA311] hover:text-[#29354d] transition-all duration-200 shadow-md shadow-[#FCA311]/20"
          >
            <FaLinkedinIn size={18} />
          </a>
          <a
            href="#"
            aria-label="Instagram"
            className="group rounded-full border border-[#FCA311]/30 p-2 hover:bg-[#FCA311] hover:text-[#29354d] transition-all duration-200 shadow-md shadow-[#FCA311]/20"
          >
            <FaInstagram size={18} />
          </a>
          <a
            href="#"
            aria-label="Twitter"
            className="group rounded-full border border-[#FCA311]/30 p-2 hover:bg-[#FCA311] hover:text-[#29354d] transition-all duration-200 shadow-md shadow-[#FCA311]/20"
          >
            <FaTwitter size={18} />
          </a>
        </div>
        <p className="text-xs text-gray-300 mt-2 text-center md:text-left">
          Stay connected with us for updates!
        </p>
      </div>
    </div>

    {/* Divider */}
    <div className="relative z-10 mt-12 border-t border-[#FCA311]/20" />

    {/* Bottom Bar */}
    <div className="relative z-10 mt-6 text-center text-xs md:text-sm text-gray-300">
      <span>
        © {new Date().getFullYear()}{" "}
        <span className="text-white font-semibold">Proyuj</span>. All Rights
        Reserved.
      </span>
      <div className="mt-2 flex flex-col sm:flex-row justify-center items-center gap-2 sm:gap-0">
        <a
          href="#"
          className="hover:text-[#FCA311] mx-2 transition-all duration-200"
        >
          Privacy Policy
        </a>
        <span className="hidden sm:inline-block text-[#FCA311] font-bold">
          |
        </span>
        <a
          href="#"
          className="hover:text-[#FCA311] mx-2 transition-all duration-200"
        >
          Terms & Conditions
        </a>
      </div>
    </div>
  </footer>
);

export default Footer;
