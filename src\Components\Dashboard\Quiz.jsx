import React from "react";
import { FaClipboardList, FaExternalLinkAlt } from "react-icons/fa";

const Quiz = ({ quizData }) => (
  <div className="grid gap-8 grid-cols-1 md:grid-cols-2">
    {quizData.map((item, idx) => (
      <div
        key={idx}
        className="bg-white border border-blue-100 rounded-2xl p-7 flex flex-col shadow-md hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
      >
        <div className="flex items-center gap-3 mb-3">
          <div className="bg-blue-100 text-blue-600 rounded-full p-2">
            <FaClipboardList className="text-2xl" />
          </div>
          <h2 className="text-xl font-bold text-blue-700">{item.title}</h2>
        </div>
        <p className="text-gray-600 mb-6">{item.description}</p>
        <a
          href={item.link}
          target="_blank"
          rel="noopener noreferrer"
          className="mt-auto inline-flex items-center gap-2 px-5 py-2 rounded-lg bg-blue-600 text-white font-semibold shadow hover:bg-blue-700 transition"
        >
          Take Quiz <FaExternalLinkAlt className="ml-1" />
        </a>
      </div>
    ))}
  </div>
);

export default Quiz;
