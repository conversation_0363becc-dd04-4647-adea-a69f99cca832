import React, { useState } from "react";
import {
  FaUserCircle,
  FaEdit,
  FaSave,
  FaPlus,
  FaMinus,
  FaTimes,
} from "react-icons/fa";
import { useAuth0 } from "@auth0/auth0-react";

const SUGGESTED_FIELDS = [
  "skills",
  "projects",
  "certifications",
  "linkedin",
  "github",
  "portfolio",
  "languages",
  "interests",
];

const colorPill = (key) => {
  // Assign custom colors to well-known keys
  const map = {
    skills: "bg-purple-100 text-purple-800",
    projects: "bg-green-100 text-green-800",
    certifications: "bg-yellow-100 text-yellow-800",
    linkedin: "bg-blue-100 text-blue-800",
    github: "bg-gray-100 text-gray-800",
    portfolio: "bg-pink-100 text-pink-800",
    languages: "bg-orange-100 text-orange-800",
    interests: "bg-teal-100 text-teal-800",
  };
  return map[key] || "bg-gray-100 text-gray-800";
};

const Profile = ({ user: initialUser }) => {
  const [user, setUser] = useState(initialUser || {});
  const [editMode, setEditMode] = useState(false);
  const [formState, setFormState] = useState({
    name: user?.name || "",
    email: user?.email || "",
    department: user?.department || "",
    role: user?.role || "",
    about: user?.about || "",
    customFields: user?.customFields || {},
  });
  const [customFieldKey, setCustomFieldKey] = useState("");
  const [customFieldValue, setCustomFieldValue] = useState("");
  const [status, setStatus] = useState("");
  const { getAccessTokenSilently } = useAuth0();

  // Handle standard field changes
  const handleChange = (e) => {
    setFormState((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  // Handle custom field changes
  const handleCustomFieldChange = (key, value) => {
    setFormState((prev) => ({
      ...prev,
      customFields: {
        ...prev.customFields,
        [key]: value,
      },
    }));
  };

  // Add a new custom field
  const handleAddCustomField = () => {
    if (!customFieldKey.trim()) return;
    setFormState((prev) => ({
      ...prev,
      customFields: {
        ...prev.customFields,
        [customFieldKey.trim()]: customFieldValue,
      },
    }));
    setCustomFieldKey("");
    setCustomFieldValue("");
  };

  // Remove a custom field
  const handleRemoveCustomField = (key) => {
    setFormState((prev) => {
      const newFields = { ...prev.customFields };
      delete newFields[key];
      return { ...prev, customFields: newFields };
    });
  };

  const handleEdit = () => {
    setFormState({
      name: user?.name || "",
      email: user?.email || "",
      department: user?.department || "",
      role: user?.role || "",
      about: user?.about || "",
      customFields: user?.customFields || {},
    });
    setEditMode(true);
    setStatus("");
  };

  const handleCancel = () => {
    setEditMode(false);
    setStatus("");
  };

  const handleSave = async (e) => {
    e.preventDefault();
    setStatus("Saving...");
    try {
      const token = await getAccessTokenSilently({
        audience: "https://interviewbackend-zfwp.onrender.com/api",
        scope: "openid profile email",
      });

      const res = await fetch(
        "https://interviewbackend-zfwp.onrender.com/api/user/profile",
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(formState),
        }
      );
      if (!res.ok) throw new Error("Failed to update profile");
      const data = await res.json();
      setUser(data.user);
      setEditMode(false);
      setStatus("Profile updated!");
    } catch (err) {
      console.log(err);
      setStatus("Error updating profile");
    }
  };

  // --- SUGGESTED FIELDS LOGIC ---
  const missingSuggestions = SUGGESTED_FIELDS.filter(
    (field) => !Object.keys(formState.customFields || {}).includes(field)
  );

  // --- Design improvement variables ---
  const borderStyle = "border bg-white rounded-3xl shadow-lg";
  const sectionTitle = "font-semibold text-blue-900 text-lg tracking-wide";
  const labelStyle = "block mb-1 text-blue-700 font-medium";
  const inputStyle =
    "w-full border rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-200 transition";
  const actionBtn =
    "px-5 py-2 rounded-lg font-semibold transition focus:outline-none";
  const saveBtn =
    "bg-gradient-to-r from-blue-600 to-blue-400 text-white hover:from-blue-700 hover:to-blue-500 " +
    actionBtn;
  const cancelBtn =
    "bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-300 " +
    actionBtn;

  // --- Helper for displaying field values as chips/links when possible ---
  const renderFieldValue = (key, value) => {
    // Show links for certain keys
    if (
      ["linkedin", "github", "portfolio"].includes(key) &&
      typeof value === "string"
    ) {
      let href = value;
      if (!/^https?:\/\//i.test(href)) href = "https://" + href;
      return (
        <a
          href={href}
          target="_blank"
          rel="noopener noreferrer"
          className="underline text-blue-600 hover:text-blue-800"
        >
          {value}
        </a>
      );
    }
    // Display arrays as comma-separated pills
    if (
      Array.isArray(value) ||
      (typeof value === "string" && value.includes(","))
    ) {
      const values = Array.isArray(value)
        ? value
        : value.split(",").map((v) => v.trim());
      return (
        <div className="flex flex-wrap gap-1">
          {values.map((v, idx) =>
            v ? (
              <span
                key={idx}
                className={
                  "inline-block px-2 py-0.5 rounded-xl text-xs font-semibold " +
                  colorPill(key)
                }
              >
                {v}
              </span>
            ) : null
          )}
        </div>
      );
    }
    // Show objects as JSON
    if (typeof value === "object") return <span>{JSON.stringify(value)}</span>;
    // Default
    return <span>{value}</span>;
  };

  return (
    <div className="flex flex-col items-center justify-center w-full max-w-2xl mx-auto py-8">
      <div className="relative">
        <FaUserCircle className="text-8xl mb-4 text-blue-300 drop-shadow" />
        {!editMode ? (
          <button
            className="absolute top-3 right-0 p-2 bg-white rounded-full shadow hover:bg-blue-50 text-blue-700 transition"
            onClick={handleEdit}
            title="Edit Profile"
          >
            <FaEdit />
          </button>
        ) : null}
      </div>
      <div className={borderStyle + " w-full p-0 md:p-10"}>
        {!editMode ? (
          <div className="w-full">
            <h2 className="text-4xl font-extrabold mb-1 text-blue-900 tracking-tight">
              {user?.name || "Loading..."}
            </h2>
            <div className="text-gray-600 mb-3 text-lg">
              {user?.email || ""}
            </div>
            <div className="flex flex-wrap gap-3 mb-4 text-gray-500">
              <span>
                <span className="font-semibold">Department:</span>{" "}
                {user?.department || "N/A"}
              </span>
              <span>|</span>
              <span>
                <span className="font-semibold">Role:</span>{" "}
                {user?.role || "N/A"}
              </span>
            </div>
            <div className="bg-blue-50/80 rounded-xl p-5 mb-6 border border-blue-100 text-gray-700">
              <span className="font-semibold text-blue-900">About:</span>{" "}
              {user?.about ||
                "Enthusiastic learner, passionate about coding and career growth."}
            </div>
            {/* Custom Fields */}
            {user?.customFields &&
              Object.keys(user.customFields).length > 0 && (
                <div className="mb-6">
                  <h3 className={sectionTitle + " mb-2"}>Custom Sections</h3>
                  <ul className="flex flex-col gap-2">
                    {Object.entries(user.customFields).map(([key, value]) => (
                      <li key={key}>
                        <span
                          className={
                            "capitalize font-semibold mr-2 " + colorPill(key)
                          }
                        >
                          {key}:
                        </span>
                        {renderFieldValue(key, value)}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            {/* Suggestions */}
            <div className="bg-blue-50 border border-blue-100 rounded-xl p-4 mt-2 text-sm text-blue-800 flex flex-wrap gap-2 items-center">
              <b className="mr-2">Tip:</b>
              Edit your profile to add{" "}
              <span className="font-semibold">
                skills, projects, certifications, or any custom section!
              </span>
              {missingSuggestions.length > 0 && (
                <div className="flex gap-1 ml-2">
                  {missingSuggestions.map((field) => (
                    <span
                      key={field}
                      className={
                        "px-2 py-0.5 rounded-xl text-xs font-semibold " +
                        colorPill(field)
                      }
                    >
                      {field}
                    </span>
                  ))}
                </div>
              )}
            </div>
            {status && (
              <div className="mt-5 text-center text-green-600 font-semibold">
                {status}
              </div>
            )}
          </div>
        ) : (
          <form
            className="w-full space-y-5"
            onSubmit={handleSave}
            autoComplete="off"
          >
            <div className="flex flex-wrap gap-5">
              <div className="w-full md:w-1/2">
                <label className={labelStyle}>Name</label>
                <input
                  type="text"
                  name="name"
                  className={inputStyle}
                  value={formState.name}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="w-full md:w-1/2">
                <label className={labelStyle}>Email</label>
                <input
                  type="email"
                  name="email"
                  className={inputStyle}
                  value={formState.email}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="w-full md:w-1/2">
                <label className={labelStyle}>Department</label>
                <input
                  type="text"
                  name="department"
                  className={inputStyle}
                  value={formState.department}
                  onChange={handleChange}
                  placeholder="e.g. CSE, ECE, General"
                />
              </div>
              <div className="w-full md:w-1/2">
                <label className={labelStyle}>Role</label>
                <input
                  type="text"
                  name="role"
                  className={inputStyle}
                  value={formState.role}
                  onChange={handleChange}
                  placeholder="e.g. Student, Candidate"
                />
              </div>
            </div>
            <div>
              <label className={labelStyle}>About</label>
              <textarea
                name="about"
                rows={3}
                className={inputStyle}
                value={formState.about}
                onChange={handleChange}
                placeholder="A short description"
                maxLength={400}
              />
            </div>
            {/* Custom Fields Editor */}
            <div>
              <label className={labelStyle}>
                Custom Sections (skills, projects, links, etc.)
              </label>
              <div className="flex flex-col md:flex-row gap-2 mb-1">
                <input
                  type="text"
                  className={inputStyle + " md:w-1/4"}
                  placeholder="Field name e.g. skills"
                  value={customFieldKey}
                  onChange={(e) => setCustomFieldKey(e.target.value)}
                />
                <input
                  type="text"
                  className={inputStyle + " md:w-2/4"}
                  placeholder="Field value e.g. JavaScript, React"
                  value={customFieldValue}
                  onChange={(e) => setCustomFieldValue(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      handleAddCustomField();
                    }
                  }}
                />
                <button
                  type="button"
                  className="bg-gradient-to-r from-blue-400 to-blue-600 text-white px-3 py-2 rounded hover:from-blue-500 hover:to-blue-700 transition"
                  onClick={handleAddCustomField}
                  title="Add Custom Field"
                >
                  <FaPlus />
                </button>
              </div>
              {/* List of current custom fields */}
              {Object.entries(formState.customFields || {}).length > 0 && (
                <ul className="flex flex-wrap gap-2 mt-1">
                  {Object.entries(formState.customFields).map(
                    ([key, value]) => (
                      <li
                        key={key}
                        className={
                          "flex items-center gap-2 px-3 py-1 rounded-xl shadow " +
                          colorPill(key)
                        }
                      >
                        <span className="capitalize font-semibold">{key}:</span>
                        <span>{renderFieldValue(key, value)}</span>
                        <button
                          type="button"
                          className="ml-1 text-red-600 hover:text-red-800"
                          onClick={() => handleRemoveCustomField(key)}
                          title="Remove"
                        >
                          <FaTimes />
                        </button>
                      </li>
                    )
                  )}
                </ul>
              )}
              {/* Suggestions for new fields */}
              {missingSuggestions.length > 0 && (
                <div className="text-xs text-gray-500 mt-2">
                  <b>Suggestions to enhance your resume:</b>{" "}
                  {missingSuggestions.map((field) => (
                    <span
                      key={field}
                      className={
                        "px-2 py-0.5 rounded-xl font-semibold " +
                        colorPill(field)
                      }
                    >
                      {field}
                    </span>
                  ))}
                </div>
              )}
            </div>
            <div className="flex gap-3 pt-2">
              <button type="submit" className={saveBtn}>
                <FaSave className="mr-1" /> Save
              </button>
              <button
                type="button"
                className={cancelBtn}
                onClick={handleCancel}
              >
                <FaMinus className="mr-1" /> Cancel
              </button>
            </div>
            {status && (
              <div className="mt-2 text-center text-red-600 font-semibold">
                {status}
              </div>
            )}
          </form>
        )}
      </div>
    </div>
  );
};

export default Profile;
