import React, { useEffect } from "react";
import { useAuth0 } from "@auth0/auth0-react";
import { DotLottieReact } from "@lottiefiles/dotlottie-react";

export default function RequireAuth({ children }) {
  const { isAuthenticated, isLoading, loginWithRedirect } = useAuth0();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      loginWithRedirect();
    }
  }, [isLoading, isAuthenticated, loginWithRedirect]);

  if (isLoading) return (
    <DotLottieReact
      src="https://lottie.host/454c3626-2618-4344-b957-5f9c8d674a99/UVood7R6b1.lottie"
      loop
      autoplay
    />
  );
  return isAuthenticated ? children : null;
}

