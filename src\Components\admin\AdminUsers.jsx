import React, { useState } from 'react';
import { motion } from 'framer-motion';
import AddUserForm from './components/AddUserForm'; // Make sure the path is correct

const AdminUsers = () => {
  const [form, setForm] = useState({ name: '', email: '', role: 'Admin' });
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  const [showForm, setShowForm] = useState(false);

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const stored = localStorage.getItem('adminUserProfile');
    if (stored) {
      const user = JSON.parse(stored);
      if (user.email === form.email) {
        setError('Email already exists');
        setSuccess(false);
        setTimeout(() => setError(''), 2000);
        return;
      }
    }
    localStorage.setItem('adminUserProfile', JSON.stringify(form));
    setSuccess(true);
    setError('');
    setForm({ name: '', email: '', role: 'Admin' });
    setTimeout(() => {
      setSuccess(false);
      setShowForm(false);
    }, 2000);
  };

  const fieldVariants = {
    hidden: { opacity: 0, scale: 1.1, y: 15 },
    visible: (i) => ({ opacity: 1, scale: 1, y: 0, transition: { delay: 0.1 + i * 0.1, duration: 0.4, type: 'spring', stiffness: 90 } })
  };

  return (
    <div className="min-h-screen w-full flex flex-col items-center justify-center bg-gradient-to-br from-gray-50 via-blue-50 to-gray-100 py-12 px-4 relative">
      <motion.div
        className="w-full max-w-xl flex flex-col items-center mb-8"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="relative bg-white rounded-2xl shadow-xl border border-gray-200 px-10 py-8 flex flex-col items-center w-full">
          <h1 className="text-3xl md:text-4xl font-extrabold text-gray-800 text-center tracking-tight mb-2">
            Create a New User
          </h1>
          <p className="text-base text-gray-600 text-center font-medium">
            Fill in the details to add a new user to the system.
          </p>
        </div>
      </motion.div>
      
      <motion.button
        className="mb-6 px-8 py-3 text-white rounded-xl font-bold shadow-lg transition-colors duration-200 text-lg focus:outline-none focus:ring-4 focus:ring-blue-300"
        style={{ background: 'rgb(35, 65, 75)' }}
        onClick={() => setShowForm(true)}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        Add User
      </motion.button>
      
      {showForm && (
        <AddUserForm
          form={form}
          success={success}
          error={error}
          handleChange={handleChange}
          handleSubmit={handleSubmit}
          setShowForm={setShowForm}
        />
      )}
    </div>
  );
};

export default AdminUsers; 