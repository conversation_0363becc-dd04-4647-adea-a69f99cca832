import React, { useState, useContext } from 'react';
import { motion } from 'framer-motion';
import { PlusCircle } from 'lucide-react';
// import { SharedDataContext } from '../../App';
import AddJobPostForm from './components/AddJobPostForm'; // Make sure the path is correct

const AdminJobPosts = () => {
  const [showForm, setShowForm] = useState(false);
  const [form, setForm] = useState({
    company: '',
    role: '',
    salary: '',
    type: 'Intern',
    workMode: 'Work from Home',
  });
  const [success, setSuccess] = useState(false);
  // const { addJobPost } = useContext(SharedDataContext);

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    addJobPost(form);
    setSuccess(true);
    setTimeout(() => {
      setShowForm(false);
      setSuccess(false);
      setForm({
        company: '',
        role: '',
        salary: '',
        type: 'Intern',
        workMode: 'Work from Home',
      });
    }, 1500);
  };

  if (showForm) {
    return (
      <AddJobPostForm
        form={form}
        success={success}
        handleChange={handleChange}
        handleSubmit={handleSubmit}
      />
    );
  }

  return (
    <div className="min-h-screen w-full flex flex-col items-center justify-center bg-gradient-to-br from-gray-50 via-blue-50 to-gray-100 py-12 px-4 relative">
      <motion.div
        className="w-full max-w-xl flex flex-col items-center mb-8"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 px-10 py-8 flex flex-col items-center w-full">
          <h1 className="text-3xl md:text-4xl font-extrabold text-gray-800 dark:text-gray-200 text-center tracking-tight mb-2">
            Create a New Job Post
          </h1>
          <p className="text-base text-gray-600 dark:text-gray-400 text-center font-medium">
            Fill in the details to create a new job opening.
          </p>
        </div>
      </motion.div>

      <motion.button
        className="flex items-center gap-3 mb-6 px-8 py-3 text-white rounded-xl font-bold shadow-lg transition-colors duration-200 text-lg focus:outline-none focus:ring-4 focus:ring-blue-300"
        style={{ background: 'rgb(35, 65, 75)' }}
        onClick={() => setShowForm(true)}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <PlusCircle size={24} />
        Add Job Post
      </motion.button>
    </div>
  );
};

export default AdminJobPosts; 