import React, { useEffect } from 'react';
import { DotLottieReact } from "@lottiefiles/dotlottie-react";
import useAuthStore from '../../store/authStore';

/**
 * AuthProvider component that initializes authentication state on app startup
 * This component should wrap the entire app to ensure auth state is initialized
 * before any routes are rendered
 */
const AuthProvider = ({ children }) => {
  const { initialized, loading, initialize } = useAuthStore();

  useEffect(() => {
    // Initialize authentication state when the app starts
    if (!initialized) {
      initialize();
    }
  }, [initialized, initialize]);

  // Show loading screen while initializing authentication
  if (!initialized || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-100 to-purple-200">
        <div className="text-center">
          <DotLottieReact
            src="https://lottie.host/454c3626-2618-4344-b957-5f9c8d674a99/UVood7R6b1.lottie"
            loop
            autoplay
            style={{ width: 200, height: 200 }}
          />
          <p className="mt-4 text-gray-600 font-medium">Initializing application...</p>
        </div>
      </div>
    );
  }

  return children;
};

export default AuthProvider;
