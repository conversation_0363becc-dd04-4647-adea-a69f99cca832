import React, { useState, useEffect } from "react";
import { FaBars, FaTimes, FaUser, FaSignOutAlt, FaCog } from "react-icons/fa";
import { motion, AnimatePresence } from "framer-motion";
import { Link, useNavigate, useLocation } from "react-router-dom";
import useAuth from "../../hooks/useAuth";

// Navigation items for different user roles
const getNavItemsByRole = (role, isAuthenticated) => {
  const publicItems = [
    { name: "Home", path: "/" },
    { name: "About Us", path: "/about" },
    { name: "Services", path: "/services" },
    { name: "How It Works", path: "/how-it-works" },
    { name: "Contact", path: "/contact" },
  ];

  if (!isAuthenticated) {
    return publicItems;
  }

  switch (role) {
    case 'admin':
      return [
        { name: "Dashboard", path: "/admin-dashboard" },
        { name: "Companies", path: "/admin-dashboard/companies" },
        { name: "Job Posts", path: "/admin-dashboard/job-posts" },
        { name: "Users", path: "/admin-dashboard/users" },
        { name: "<PERSON><PERSON><PERSON>", path: "/admin-dashboard/settings" },
      ];
    
    case 'company':
      return [
        { name: "Dashboard", path: "/dashboard" },
        { name: "Create Job", path: "/job-create" },
        { name: "Test Management", path: "/test-management" },
        { name: "Questions", path: "/aptitude" },
        { name: "Interview", path: "/interview" },
        { name: "Profile", path: "/profile" },
      ];
    
    case 'student':
      return [
        { name: "Dashboard", path: "/student-dashboard" },
        { name: "Take Test", path: "/test" },
        { name: "Test Results", path: "/test-result" },
        { name: "Interview Prep", path: "/interview-prep" },
        { name: "Profile", path: "/student-profile" },
      ];
    
    default:
      return publicItems;
  }
};

const navVariants = {
  hidden: { y: -70, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.6, ease: "easeOut" } },
};

const mobileItemVariants = {
  hidden: { x: -30, opacity: 0 },
  visible: (i) => ({
    x: 0,
    opacity: 1,
    transition: {
      delay: i * 0.07 + 0.1,
      type: "spring",
      stiffness: 300,
      damping: 24,
    },
  }),
};

const RoleBasedNavbar = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  
  const { 
    user, 
    isAuthenticated, 
    loading, 
    logout, 
    redirectToDashboard 
  } = useAuth();

  const handleToggle = () => setMenuOpen((prev) => !prev);
  const handleNavClick = () => setMenuOpen(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Get navigation items based on user role
  const navItems = getNavItemsByRole(user?.role, isAuthenticated);
  
  // Text color logic
  const textColor = scrolled ? "text-gray-800" : "text-white";
  
  // Logo click handler
  const handleLogoClick = () => {
    if (isAuthenticated && user) {
      redirectToDashboard();
    } else {
      navigate("/");
    }
  };

  // Logout handler
  const handleLogout = async () => {
    await logout();
    setMenuOpen(false);
  };

  // Login handler
  const handleLogin = () => {
    navigate("/login");
    setMenuOpen(false);
  };

  // Register handler
  const handleRegister = () => {
    navigate("/register");
    setMenuOpen(false);
  };

  return (
    <motion.nav
      variants={navVariants}
      initial="hidden"
      animate="visible"
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled
          ? "bg-white/95 backdrop-blur-lg shadow-md py-4"
          : "bg-transparent py-3"
      }`}
    >
      <div className="max-w-7xl mx-auto px-6 flex justify-between items-center">
        {/* Logo */}
        <button
          onClick={handleLogoClick}
          className={`text-2xl font-bold tracking-tight ${textColor} hover:opacity-80 transition-opacity`}
        >
          Proyuj
        </button>

        {/* Desktop Menu */}
        <ul className="hidden lg:flex space-x-8 items-center">
          {navItems.map((item) => (
            <li key={item.name}>
              <Link
                to={item.path}
                className={`${textColor} hover:text-[#fcb045] font-medium transition-colors duration-300 ${
                  location.pathname === item.path ? 'text-[#fcb045]' : ''
                }`}
              >
                {item.name}
              </Link>
            </li>
          ))}
          
          {/* Authentication Buttons */}
          <li>
            {!loading && !isAuthenticated ? (
              <div className="flex space-x-3">
                <button
                  onClick={handleLogin}
                  className="bg-[#fcb045] text-white px-6 py-2 rounded-full font-bold shadow hover:scale-105 transition-transform duration-300"
                >
                  <FaUser className="inline-block mr-2" />
                  Login
                </button>
                <button
                  onClick={handleRegister}
                  className="bg-[#34c759] text-white px-6 py-2 rounded-full font-bold shadow hover:scale-105 transition-transform duration-300"
                >
                  <FaUser className="inline-block mr-2" />
                  Sign Up
                </button>
              </div>
            ) : isAuthenticated && user ? (
              <div className="flex items-center space-x-4">
                <span className={`${textColor} font-medium`}>
                  Welcome, {user.name || user.email}
                </span>
                <span className={`${textColor} text-sm opacity-75 capitalize`}>
                  ({user.role})
                </span>
                <button
                  onClick={handleLogout}
                  className="bg-red-500 text-white px-4 py-2 rounded-full font-medium shadow hover:scale-105 transition-transform duration-300"
                >
                  <FaSignOutAlt className="inline-block mr-2" />
                  Logout
                </button>
              </div>
            ) : null}
          </li>
        </ul>

        {/* Mobile Menu Button */}
        <button
          onClick={handleToggle}
          className={`lg:hidden p-2 rounded-md ${textColor} hover:bg-white/10 transition-colors`}
          aria-label="Toggle menu"
        >
          {menuOpen ? <FaTimes size={24} /> : <FaBars size={24} />}
        </button>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {menuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="lg:hidden bg-white/95 backdrop-blur-lg border-t border-gray-200"
          >
            <div className="px-6 py-4 space-y-3">
              {navItems.map((item, index) => (
                <motion.div
                  key={item.name}
                  custom={index}
                  variants={mobileItemVariants}
                  initial="hidden"
                  animate="visible"
                >
                  <Link
                    to={item.path}
                    onClick={handleNavClick}
                    className={`block py-2 text-gray-800 hover:text-[#fcb045] font-medium transition-colors ${
                      location.pathname === item.path ? 'text-[#fcb045]' : ''
                    }`}
                  >
                    {item.name}
                  </Link>
                </motion.div>
              ))}
              
              {/* Mobile Authentication */}
              <div className="pt-4 border-t border-gray-200">
                {!loading && !isAuthenticated ? (
                  <div className="space-y-3">
                    <button
                      onClick={handleLogin}
                      className="w-full bg-[#fcb045] text-white px-6 py-3 rounded-full font-bold shadow"
                    >
                      <FaUser className="inline-block mr-2" />
                      Login
                    </button>
                    <button
                      onClick={handleRegister}
                      className="w-full bg-[#34c759] text-white px-6 py-3 rounded-full font-bold shadow"
                    >
                      <FaUser className="inline-block mr-2" />
                      Sign Up
                    </button>
                  </div>
                ) : isAuthenticated && user ? (
                  <div className="space-y-3">
                    <div className="text-gray-800 font-medium">
                      Welcome, {user.name || user.email}
                      <span className="block text-sm opacity-75 capitalize">
                        Role: {user.role}
                      </span>
                    </div>
                    <button
                      onClick={handleLogout}
                      className="w-full bg-red-500 text-white px-6 py-3 rounded-full font-medium shadow"
                    >
                      <FaSignOutAlt className="inline-block mr-2" />
                      Logout
                    </button>
                  </div>
                ) : null}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.nav>
  );
};

export default RoleBasedNavbar;
