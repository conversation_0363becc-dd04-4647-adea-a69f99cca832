import React, { useState, useEffect } from "react";
import { useAuth0 } from "@auth0/auth0-react";
import Navbar from "../Navbar";
import Footer from "../Footer";
// useAuth is used by the imported components
import { SidebarUserInfo } from "../../components/common/UserInfo";
import { SidebarLogoutButton } from "../../components/common/LogoutButton";

import Profile from "./Profile";
import Resume from "./ResumeDetails";
import Quiz from "./Quiz";
import Interview from "./interview"; // <-- Make sure you have this component!


import {
  FaUserCircle,
  FaChalkboardTeacher,
  FaClipboardList,
  FaFileAlt,
  FaUser,
  FaSignOutAlt,
} from "react-icons/fa";

// Dummy data for Quiz and Interview sections
const quizData = [
  {
    title: "Aptitude Test",
    description: "Test your logical reasoning and math skills.",
    link: "https://appttitude.proyuj.com/",
  },
  {
    title: "Technical Quiz",
    description: "Assess your programming and computer science knowledge.",
    link: "https://appttitude.proyuj.com/",
  },
];

const interviewData = [
  {
    title: "Mock Interview Portal",
    description: "Practice with real-time interview questions.",
    link: "https://interviewpage-inky.vercel.app/",
  },
  {
    title: "HR Interview Prep",
    description: "Prepare for HR interview rounds.",
    link: "https://interviewpage-inky.vercel.app/",
  },
];

const menuItems = [
  { label: "Interview", icon: <FaChalkboardTeacher /> },
  { label: "Quiz", icon: <FaClipboardList /> },
  { label: "Resume Building", icon: <FaFileAlt /> },
  { label: "Profile", icon: <FaUser /> },
];

const Studentdashboard = () => {
  const [selected, setSelected] = useState(0);
  const [resume, setResume] = useState({
    name: "",
    email: "",
    summary: "",
    skills: "",
    education: "",
    experience: "",
  });
  const [user, setUser] = useState(null);
  const { getAccessTokenSilently, isAuthenticated } = useAuth0();
  // Auth hook is used by the imported components

  // Fetch user profile from backend
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const token = await getAccessTokenSilently({
          audience: "https://interviewbackend-zfwp.onrender.com/api",
          scope: "openid profile email",
        });
        const res = await fetch(
          "https://interviewbackend-zfwp.onrender.com/api/auth0-bridge",
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        );
        const data = await res.json();
        setUser(data.user);
      } catch (error) {
        console.error("Failed to fetch user profile:", error);
      }
    };

    if (isAuthenticated) {
      fetchUserProfile();
    }
  }, [isAuthenticated, getAccessTokenSilently]);

  // Render section content based on selected sidebar item
  const renderSection = () => {
    switch (selected) {
      case 0:
        return <Interview interviewData={interviewData} />;
      case 1:
        return <Quiz quizData={quizData} />;
      case 2:
        return <Resume resume={resume} setResume={setResume} />;
      case 3:
        return <Profile user={user} />;
      default:
        return null;
    }
  };

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-gray-50 flex">
        {/* Sidebar */}
        <aside className="w-60 bg-white border-r border-gray-200 flex flex-col py-6 px-4 shadow-sm">
          <div className="mb-8">
            <SidebarUserInfo
              variant="sidebar"
              className="text-gray-800"
              iconType="fa"
            />
          </div>
          <nav className="flex-1">
            <ul>
              {menuItems.map((item, idx) => (
                <li
                  key={item.label}
                  className={`flex items-center gap-3 px-4 py-3 rounded-lg mb-2 cursor-pointer transition-all ${
                    idx === selected
                      ? "bg-blue-100 font-bold text-blue-700"
                      : "hover:bg-blue-50 text-gray-700"
                  }`}
                  onClick={() => setSelected(idx)}
                >
                  {item.icon}
                  <span>{item.label}</span>
                </li>
              ))}
            </ul>
          </nav>
          {/* Logout Button */}
          <div className="mt-auto pt-6 border-t border-gray-100">
            <SidebarLogoutButton
              className="w-full flex items-center justify-center gap-2 px-4 py-3 text-red-600 hover:text-white hover:bg-red-600 rounded-lg transition-all duration-200 font-medium"
              iconType="fa"
            />
            <div className="text-center text-xs text-gray-400 mt-3">
              &copy; {new Date().getFullYear()} Student Portal
            </div>
          </div>
        </aside>
        {/* Main Content */}
        <main className="flex-1 p-8 bg-gray-50">
          <h1 className="text-2xl font-bold mb-7 text-blue-700">
            {menuItems[selected].label}
          </h1>
          <section className="bg-white rounded-xl shadow p-6 min-h-[300px] w-full max-w-4xl mx-auto flex flex-col items-center justify-center">
            {renderSection()}
          </section>
        </main>
      </div>
      <Footer />
    </>
  );
};

export default Studentdashboard;
