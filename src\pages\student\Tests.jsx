import React, { useState, useRef, useEffect } from 'react';
import { FaUserFriends, FaCalendarAlt, FaTrophy, FaRegClock, FaUser, FaTags, FaCheckCircle, FaUsers } from 'react-icons/fa';

const mockTests = [
  {
    id: 1,
    title: 'NEP 2020 Quiz',
    org: 'Thakur College of Engineering and Technology (TCET), Mumbai',
    logo: '',
    registered: 2,
    daysLeft: 5,
    tags: ['Engineering Students', 'Undergraduate'],
    moreTags: 1,
    prize: '',
    type: 'Online Quiz',
    updated: 'Jul 22, 2025',
    event: '<PERSON><PERSON><PERSON> – NEP Week (22nd to 28th July 2025)',
    status: 'Free',
    impressions: 63,
    deadline: '5 days left',
    participation: 'Individual Participation',
    eligibility: 'Engineering Students • Undergraduate',
    details: `**All that you need to know about NEP 2020 Quiz**\n\n**Guidelines:**\n- Open to **all undergraduate students across India.**\n- **Individual participation only** – team entries are not permitted.\n- Students from **any college, stream, or specialization** are welcome to participate.\n- No prior registration fee is required.\n\n**Format:**\n- Online Quiz conducted via **Google Forms**\n- Questions based on **National Education Policy (NEP) 2020** – its vision, goals, reforms, and impact\n- **Number of Questions:** 30 multiple-choice questions\n- **Marking Scheme:**\n  - Each question carries **3 marks**\n  - **No negative marking**\n- **Duration:** 30 minutes\n- **Certification Criteria:** Participants scoring **70% or above (i.e., 63 out of 90 marks)** will be eligible for an **E-Certificate**.\n- **Mode:** Entirely online\n- **Registration Window:** 22nd July 2025 – 26th July 2025 (till 5:00 PM)\n\n**Rules:**\n- Only one attempt per participant is allowed.\n- Participants must submit the quiz **within the time limit** of 30 minutes.\n- Use of unfair means such as internet searching or team participation is **strictly prohibited**.\n- All decisions made by the organizing committee will be final and binding.\n- Participants must enter their **correct name and email ID** for certificate eligibility.`,
    importantDates: [
      { label: 'Registration Deadline', value: '28 Jul 25, 12:00 PM IST', icon: <FaCalendarAlt /> },
    ],
    contact: {
      label: 'Send queries to organizers',
      url: '#',
    },
  },
  {
    id: 2,
    title: 'Price Me If You Can by InvestSchool - 4th August',
    org: 'InvestSchool',
    logo: 'https://d8it4huxumps7.cloudfront.net/uploads/images/opportunity/mobile_banner/64c0e2e2e2b7a_investschool.jpg',
    registered: 2,
    daysLeft: 13,
    tags: ['Finance', 'Awards'],
    moreTags: 3,
    prize: '₹ 1,000',
    type: '',
    updated: 'Jul 15, 2025',
    event: 'Finance Week – 4th August 2025',
    status: 'Free',
    impressions: 21,
    deadline: '13 days left',
    participation: 'Individual Participation',
    eligibility: 'Finance Students • All Years',
    details: `**All that you need to know about Price Me If You Can**\n\n**Guidelines:**\n- Open to all finance students.\n- Individual participation only.\n- No registration fee.\n\n**Format:**\n- Online Quiz\n- 20 multiple-choice questions\n- Each question carries 2 marks\n- No negative marking\n- Duration: 20 minutes\n\n**Rules:**\n- One attempt per participant.\n- Submit within the time limit.\n- No unfair means.`,
    importantDates: [
      { label: 'Registration Deadline', value: '4 Aug 25, 12:00 PM IST', icon: <FaCalendarAlt /> },
    ],
    contact: {
      label: 'Send queries to organizers',
      url: '#',
    },
  },
  {
    id: 3,
    title: 'AI Quiz Competition',
    org: 'Knowledge Institute of Technology (KIOT)',
    logo: 'https://d8it4huxumps7.cloudfront.net/uploads/images/opportunity/mobile_banner/ai-quiz.jpg',
    registered: 14,
    daysLeft: 1,
    tags: ['Online Quiz', 'Engineering Students'],
    moreTags: 5,
    prize: '₹ 5,000',
    type: '',
    updated: 'Jul 10, 2025',
    event: 'AI Week – July 2025',
    status: 'Free',
    impressions: 50,
    deadline: '1 day left',
    participation: 'Individual Participation',
    eligibility: 'Engineering Students • All Years',
    details: `**All that you need to know about AI Quiz Competition**\n\n**Guidelines:**\n- Open to all engineering students.\n- Individual participation only.\n- No registration fee.\n\n**Format:**\n- Online Quiz\n- 25 multiple-choice questions\n- Each question carries 4 marks\n- No negative marking\n- Duration: 25 minutes\n\n**Rules:**\n- One attempt per participant.\n- Submit within the time limit.\n- No unfair means.`,
    importantDates: [
      { label: 'Registration Deadline', value: '11 Jul 25, 12:00 PM IST', icon: <FaCalendarAlt /> },
    ],
    contact: {
      label: 'Send queries to organizers',
      url: '#',
    },
  },
  {
    id: 4,
    title: 'ABHI',
    org: 'Abhishek Technology (AT), Mumbai',
    logo: '',
    registered: 1,
    daysLeft: 1,
    tags: ['Engineering Students', 'Undergraduate'],
    moreTags: 1,
    prize: '',
    type: 'Offline Quiz',
    updated: 'Jul 22, 2025',
    event: 'abhi (22nd to 28th July 2025)',
    status: 'Free',
    impressions: 100,
    deadline: '5 days left',
    participation: 'Individual Participation',
    eligibility: 'Engineering Students • Undergraduate',
    details: `**All that you need to know about NEP 2020 Quiz**\n\n**Guidelines:**\n- Open to **all undergraduate students across India.**\n- **Individual participation only** – team entries are not permitted.\n- Students from **any college, stream, or specialization** are welcome to participate.\n- No prior registration fee is required.\n\n**Format:**\n- Online Quiz conducted via **Google Forms**\n- Questions based on **National Education Policy (NEP) 2020** – its vision, goals, reforms, and impact\n- **Number of Questions:** 30 multiple-choice questions\n- **Marking Scheme:**\n  - Each question carries **3 marks**\n  - **No negative marking**\n- **Duration:** 30 minutes\n- **Certification Criteria:** Participants scoring **70% or above (i.e., 63 out of 90 marks)** will be eligible for an **E-Certificate**.\n- **Mode:** Entirely online\n- **Registration Window:** 22nd July 2025 – 26th July 2025 (till 5:00 PM)\n\n**Rules:**\n- Only one attempt per participant is allowed.\n- Participants must submit the quiz **within the time limit** of 30 minutes.\n- Use of unfair means such as internet searching or team participation is **strictly prohibited**.\n- All decisions made by the organizing committee will be final and binding.\n- Participants must enter their **correct name and email ID** for certificate eligibility.`,
    importantDates: [
      { label: 'Registration Deadline', value: '28 Jul 25, 12:00 PM IST', icon: <FaCalendarAlt /> },
    ],
    contact: {
      label: 'Send queries to organizers',
      url: '#',
    },
  },
];

const filters = [
  { label: 'Quizzes' },
  { label: 'Sort By' },
  
];

const sortOptions = [
  { label: 'Prizes (High to Low)', value: 'prizes' },
  { label: 'Days Left (Low to high)', value: 'daysLeft' },
];

function Markdown({ children }) {
  // Simple markdown to HTML for bullets and bold
  const html = children
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\n- /g, '<ul><li>')
    .replace(/\n/g, '<br/>')
    .replace(/<ul><li>(.*?)<br\/>/g, '<ul><li>$1</li>')
    .replace(/<br\/>/g, '')
    .replace(/<ul><li>/g, '<ul><li>')
    .replace(/<\/li><li>/g, '</li><li>')
    .replace(/<\/li><\/ul>/g, '</li></ul>');
  return <div className="prose max-w-none text-gray-800" dangerouslySetInnerHTML={{ __html: html }} />;
}

export default function Tests() {
  const [selected, setSelected] = useState(mockTests[0]);
  const [activeFilter, setActiveFilter] = useState('Quizzes');
  const [sortOpen, setSortOpen] = useState(false);
  const [selectedSort, setSelectedSort] = useState('daysLeft');
  const sortRef = useRef();

  // Close dropdown on outside click
  useEffect(() => {
    function handleClickOutside(event) {
      if (sortRef.current && !sortRef.current.contains(event.target)) {
        setSortOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Sort logic
  const sortedTests = [...mockTests].sort((a, b) => {
    if (selectedSort === 'prizes') return (b.prize || 0) - (a.prize || 0);
    if (selectedSort === 'daysLeft') return (a.daysLeft || 0) - (b.daysLeft || 0);
    return 0;
  });

  return (
    <div className="min-h-screen bg-gray-50 p-0 md:p-8">
      {/* Top Bar */}
      <div className="flex flex-wrap gap-3 items-center mb-6 px-2 md:px-0">
        <button className="bg-[#23414c] text-white font-semibold px-6 py-2 rounded-full">Quizzes</button>
        <div className="relative" ref={sortRef}>
          <button
            className="bg-white border px-6 py-2 rounded-full font-semibold flex items-center gap-2"
            onClick={() => setSortOpen((open) => !open)}
          >
            Sort By <span className="text-xs">▼</span>
          </button>
          {sortOpen && (
            <div className="absolute left-0 mt-2 w-56 bg-white rounded-xl shadow-lg z-10 py-2">
              {sortOptions.map((opt) => (
                <button
                  key={opt.value}
                  className={`w-full text-left px-4 py-2 hover:bg-[#23414c]/10 flex items-center gap-2 ${
                    selectedSort === opt.value ? 'text-[#23414c] font-bold' : 'text-gray-700'
                  }`}
                  onClick={() => {
                    setSelectedSort(opt.value);
                    setSortOpen(false);
                  }}
                >
                  {opt.label}
                  {selectedSort === opt.value && (
                    <span className="ml-auto text-[#23414c] text-lg">✓</span>
                  )}
                </button>
              ))}
            </div>
          )}
        </div>
        
      </div>
      {/* Split View */}
      <div className="flex flex-col md:flex-row gap-6">
        {/* Left: List */}
        <div className="flex-1 max-w-xl overflow-y-auto" style={{ maxHeight: 'calc(100vh - 120px)' }}>
          {sortedTests.map(test => (
            <div
              key={test.id}
              className={`bg-white rounded-xl shadow p-5 mb-4 border-l-4 cursor-pointer transition-all ${selected && selected.id === test.id ? 'border-[#23414c]' : 'border-transparent'}`}
              onClick={() => setSelected(test)}
            >
              <div className="flex items-center gap-4 mb-2">
                <img src={test.logo} alt={test.title} className="w-14 h-14 rounded object-cover border" />
                <div>
                  <div className="font-bold text-lg text-[#23414c] mb-1">{test.title}</div>
                  <div className="text-gray-600 text-sm">{test.org}</div>
                  <div className="flex items-center gap-4 text-gray-500 text-xs mt-1">
                    {test.prize && <span className="flex items-center gap-1"><FaTrophy className="text-yellow-500" /> {test.prize}</span>}
                    <span className="flex items-center gap-1"><FaUserFriends /> {test.registered} Registered</span>
                    <span className="flex items-center gap-1"><FaRegClock /> {test.daysLeft} days left</span>
                  </div>
                  <div className="flex gap-2 mt-2 flex-wrap">
                    {test.tags.map((tag, i) => <span key={i} className="bg-gray-100 rounded-full px-3 py-1 text-xs font-semibold text-gray-700">{tag}</span>)}
                    {test.moreTags > 0 && <span className="bg-gray-100 rounded-full px-3 py-1 text-xs font-semibold text-gray-700">+{test.moreTags}</span>}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        {/* Right: Details */}
        <div className="flex-1 bg-white rounded-xl shadow p-8 min-w-[350px] overflow-y-auto" style={{ maxHeight: 'calc(100vh - 120px)' }}>
          {selected && (
            <>
              <div className="flex items-center gap-4 mb-4">
                <img src={selected.logo} alt={selected.title} className="w-20 h-20 rounded object-cover border" />
                <div>
                  <div className="font-bold text-3xl text-[#23414c] mb-1">{selected.title}</div>
                  <div className="text-gray-600 text-sm">{selected.org}</div>
                  {selected.event && <div className="text-gray-500 text-xs mt-1">{selected.event}</div>}
                  <div className="flex items-center gap-2 text-gray-500 text-xs mt-1">
                    <FaCalendarAlt /> Updated On: {selected.updated}
                  </div>
                </div>
              </div>
              <div className="flex gap-2 mb-4 flex-wrap">
                {selected.type && <span className="border rounded-full px-3 py-1 text-xs font-semibold text-gray-700">{selected.type}</span>}
                <span className="border rounded-full px-3 py-1 text-xs font-semibold text-gray-700">Online Quiz</span>
              </div>
              <div className="flex items-center gap-4 mb-4">
                <span className="font-bold text-2xl text-[#23414c]">{selected.status || 'Free'}</span>
                <button className="ml-auto bg-[#23414c] text-white font-bold px-8 py-2 rounded-lg">Register</button>
              </div>
              <div className="flex gap-4 mb-4">
                <div className="flex flex-col items-center bg-gray-100 rounded-lg px-4 py-2">
                  <span className="text-xs text-gray-500">Registration Deadline</span>
                  <span className="font-bold text-[#23414c]">{selected.deadline}</span>
                </div>
                <div className="flex flex-col items-center bg-gray-100 rounded-lg px-4 py-2">
                  <span className="text-xs text-gray-500">Impressions</span>
                  <span className="font-bold text-[#23414c]">{selected.impressions}</span>
                </div>
                <div className="flex flex-col items-center bg-gray-100 rounded-lg px-4 py-2">
                  <span className="text-xs text-gray-500">Individual</span>
                  <span className="font-bold text-[#23414c]">Participation</span>
                </div>
              </div>
              <div className="mb-4">
                <div className="font-bold mb-1">Eligibility</div>
                <div className="text-gray-700 text-sm">{selected.eligibility}</div>
              </div>
              {/* Details Section */}
              {selected.details && (
                <div className="bg-gray-50 rounded-xl p-6 mb-4">
                  <Markdown>{selected.details}</Markdown>
                </div>
              )}
              {/* Important Dates & Deadlines */}
              {selected.importantDates && (
                <div className="bg-gray-50 rounded-xl p-6 mb-4">
                  <div className="font-bold mb-2">Important dates & deadlines?</div>
                  {selected.importantDates.map((d, i) => (
                    <div key={i} className="flex items-center gap-2 text-gray-700 mb-1">
                      <span className="text-lg">{d.icon}</span>
                      <span className="font-semibold">{d.label}</span>
                      <span className="ml-2">{d.value}</span>
                    </div>
                  ))}
                </div>
              )}
              {/* Contact Organisers */}
              {selected.contact && (
                <div className="bg-gray-50 rounded-xl p-6 mb-4">
                  <div className="font-bold mb-2">Contact the organisers</div>
                  <a href={selected.contact.url} className="inline-flex items-center gap-2 px-4 py-2 border rounded-lg text-[#23414c] font-semibold hover:bg-blue-50" target="_blank" rel="noopener noreferrer">
                    <FaUser /> {selected.contact.label} <span className="ml-1">↗</span>
                  </a>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
} 