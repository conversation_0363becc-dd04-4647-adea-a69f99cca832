import React, { useRef } from "react";
import { FaPlay, FaBrain, FaCheckCircle } from "react-icons/fa";
import { motion, useInView } from "framer-motion";
import useScrollDirection from "../hooks/useScrollDirection";

const YELLOW = "#fcc250";
const BLUE = "#29354d";

const CARDS = [
  {
    icon: <FaPlay className="text-4xl" style={{ color: YELLOW }} />,
    title: "Live Mock Interviews",
    desc: "Real-time practice with industry experts and instant feedback.",
    color: YELLOW,
  },
  {
    icon: <FaBrain className="text-4xl" style={{ color: BLUE }} />,
    title: "Smart Aptitude Training",
    desc: "AI-powered learning paths tailored to your needs.",
    color: BLUE,
  },
  {
    icon: <FaCheckCircle className="text-4xl" style={{ color: YELLOW }} />,
    title: "Proven Success Methods",
    desc: "Strategies and frameworks that guarantee results.",
    color: YELLOW,
  },
];

const cardVariant = {
  hidden: (custom) => ({
    opacity: 0,
    x: custom.scrollDirection === "down" ? 80 : -80,
    y: 60,
    scale: 0.9,
  }),
  visible: {
    opacity: 1,
    x: 0,
    y: 0,
    scale: 1,
    transition: {
      duration: 1.2,
      ease: "easeOut",
    },
  },
};

const containerVariant = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.4,
      delayChildren: 0.3,
    },
  },
};

const AnimatedCardsSection = () => {
  const scrollDirection = useScrollDirection();
  const ref = useRef(null);
  const isInView = useInView(ref, {
    margin: "-20% 0px",
    once: false,
  });

  return (
    <section
      className="relative w-full py-20 flex flex-col items-center"
      id="animated-cards"
    >
      <h2 className="text-2xl md:text-3xl font-extrabold mb-10 text-[#29354d]">
        Why Choose Proyuj?
      </h2>

      <motion.div
        ref={ref}
        className="flex flex-col md:flex-row gap-12 w-full max-w-5xl justify-center items-stretch"
        variants={containerVariant}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
      >
        {CARDS.map((card, idx) => (
          <motion.div
            key={card.title}
            custom={{ idx, scrollDirection }}
            variants={cardVariant}
            className="flex-1 bg-white rounded-3xl border-2 hover:scale-[1.03] transition-transform duration-300 shadow-xl hover:shadow-2xl"
            style={{
              borderColor: card.color,
              minWidth: 320,
              padding: "2rem",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              background: "linear-gradient(135deg, #ffffff 60%, #f9fafb 100%)",
            }}
          >
            <div
              className="mb-6 w-20 h-20 rounded-full flex items-center justify-center border-4 shadow-lg"
              style={{
                borderColor: card.color,
                background: `linear-gradient(135deg, #fffbe9 0%, ${card.color}22 100%)`,
              }}
            >
              {card.icon}
            </div>
            <h3 className="text-xl font-extrabold mb-3 text-[#29354d] text-center">
              {card.title}
            </h3>
            <p className="text-[#4b5563] text-base text-center leading-relaxed">
              {card.desc}
            </p>
          </motion.div>
        ))}
      </motion.div>
    </section>
  );
};

export default AnimatedCardsSection;
