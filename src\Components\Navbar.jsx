import React, { useState, useEffect } from "react";
import { FaBars, FaTimes, FaUser } from "react-icons/fa";
import { motion, AnimatePresence } from "framer-motion";
import { Link, useNavigate } from "react-router-dom";
import { useAuth0 } from "@auth0/auth0-react";

const NAV_ITEMS = [
  { name: "About Us", path: "/about" },
  { name: "Services", path: "/Services" },
  { name: "How It Works", path: "/howItWorks" },
  { name: "Contact", path: "/contact" },
];

const navVariants = {
  hidden: { y: -70, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.6, ease: "easeOut" } },
};

const mobileItemVariants = {
  hidden: { x: -30, opacity: 0 },
  visible: (i) => ({
    x: 0,
    opacity: 1,
    transition: {
      delay: i * 0.07 + 0.1,
      type: "spring",
      stiffness: 300,
      damping: 24,
    },
  }),
};

const Navbar = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const navigate = useNavigate();

  const { loginWithRedirect, logout, isAuthenticated, user, isLoading } =
    useAuth0();

  const handleToggle = () => setMenuOpen((prev) => !prev);
  const handleNavClick = () => setMenuOpen(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Optional: Redirect to dashboard after login
  /*
  useEffect(() => {
    if (isAuthenticated) {
      navigate("/dashboard");
    }
  }, [isAuthenticated, navigate]);
  */

  // Text color logic
  const textColor = scrolled ? "text-gray-800" : "text-white";

  // Desktop nav items
  const desktopNavItems = isAuthenticated
    ? [{ name: "Dashboard", path: "/dashboard" }, ...NAV_ITEMS]
    : [{ name: "Home", path: "/" }, ...NAV_ITEMS];

  // Mobile nav items
  const mobileNavItems = desktopNavItems;

  return (
    <motion.nav
      variants={navVariants}
      initial="hidden"
      animate="visible"
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled
          ? "bg-white/95 backdrop-blur-lg shadow-md py-4"
          : "bg-transparent py-3"
      }`}
    >
      <div className="max-w-7xl mx-auto px-6 flex justify-between items-center">
        {/* Logo */}
        <Link
          to={isAuthenticated ? "/dashboard" : "/"}
          className={`text-2xl font-bold tracking-tight ${textColor}`}
        >
          Proyuj
        </Link>

        {/* Desktop Menu */}
        <ul className="hidden lg:flex space-x-8 items-center">
          {desktopNavItems.map((item) => (
            <li key={item.name}>
              <Link
                to={item.path}
                className="text-gray-800 hover:text-[#fcb045] font-medium transition-colors duration-300"
              >
                {item.name}
              </Link>
            </li>
          ))}
          <li>
            {!isLoading && !isAuthenticated && (
              <>
                <button
                  onClick={() => loginWithRedirect()}
                  className="ml-4 bg-[#fcb045] text-white px-6 py-2 rounded-full font-bold shadow hover:scale-105 transition-transform duration-300"
                >
                  <FaUser className="inline-block mr-2" />
                  Login
                </button>
                <button
                  onClick={() => loginWithRedirect({ screen_hint: "signup" })}
                  className="ml-4 bg-[#34c759] text-white px-6 py-2 rounded-full font-bold shadow hover:scale-105 transition-transform duration-300"
                >
                  <FaUser className="inline-block mr-2" />
                  Sign Up
                </button>
              </>
            )}
            {!isLoading && isAuthenticated && (
              <div className="flex items-center gap-2">
                <img
                  src={user.picture}
                  alt={user.name}
                  className="w-8 h-8 rounded-full border"
                />
                <span className="font-medium">{user.name}</span>
                <button
                  onClick={() =>
                    logout({
                      logoutParams: { returnTo: window.location.origin },
                    })
                  }
                  className="ml-4 bg-red-500 text-white px-6 py-2 rounded-full font-bold shadow hover:scale-105 transition-transform duration-300"
                >
                  Logout
                </button>
              </div>
            )}
          </li>
        </ul>

        {/* Hamburger */}
        <button
          aria-label="Toggle menu"
          className={`lg:hidden text-2xl ${textColor}`}
          onClick={handleToggle}
        >
          {menuOpen ? <FaTimes /> : <FaBars />}
        </button>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {menuOpen && (
          <>
            <motion.div
              key="mobile-menu"
              initial={{ x: "100%", opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: "100%", opacity: 0 }}
              transition={{ type: "spring", stiffness: 400, damping: 35 }}
              className="lg:hidden fixed top-0 left-0 w-full h-full z-50 bg-white/95 backdrop-blur-lg shadow-md"
            >
              <div className="relative z-10 flex flex-col items-center justify-center h-full space-y-10 pt-16">
                <nav className="w-full flex flex-col items-center gap-3">
                  {mobileNavItems.map((item, index) => (
                    <motion.a
                      key={item.name}
                      href={item.path}
                      className="text-xl font-medium text-gray-800 hover:text-[#fcb045] transition-colors duration-300 px-6 py-2"
                      onClick={handleNavClick}
                      custom={index}
                      initial="hidden"
                      animate="visible"
                      exit="hidden"
                      variants={mobileItemVariants}
                    >
                      {item.name}
                    </motion.a>
                  ))}
                </nav>
                {!isLoading && !isAuthenticated && (
                  <>
                    <motion.button
                      initial={{ scale: 0.9, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0.8, opacity: 0 }}
                      transition={{ delay: 0.35 }}
                      className="bg-[#fcb045] text-white px-10 py-3 rounded-full font-bold text-lg shadow hover:scale-105 transition-transform duration-300"
                      onClick={() => {
                        loginWithRedirect();
                        handleNavClick();
                      }}
                    >
                      <FaUser className="inline-block mr-2" />
                      Login
                    </motion.button>
                    <motion.button
                      initial={{ scale: 0.9, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0.8, opacity: 0 }}
                      transition={{ delay: 0.38 }}
                      className="bg-[#34c759] text-white px-10 py-3 rounded-full font-bold text-lg shadow hover:scale-105 transition-transform duration-300"
                      onClick={() => {
                        loginWithRedirect({ screen_hint: "signup" });
                        handleNavClick();
                      }}
                    >
                      <FaUser className="inline-block mr-2" />
                      Sign Up
                    </motion.button>
                  </>
                )}
                {!isLoading && isAuthenticated && (
                  <div className="flex flex-col items-center gap-3">
                    <img
                      src={user.picture}
                      alt={user.name}
                      className="w-16 h-16 rounded-full border"
                    />
                    <span className="font-medium text-lg">{user.name}</span>
                    <motion.button
                      initial={{ scale: 0.9, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0.8, opacity: 0 }}
                      transition={{ delay: 0.41 }}
                      className="bg-red-500 text-white px-10 py-3 rounded-full font-bold text-lg shadow hover:scale-105 transition-transform duration-300"
                      onClick={() => {
                        logout({
                          logoutParams: { returnTo: window.location.origin },
                        });
                        handleNavClick();
                      }}
                    >
                      Logout
                    </motion.button>
                  </div>
                )}
              </div>
            </motion.div>
            {/* Overlay */}
            <motion.div
              key="overlay"
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.4 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-40 bg-black/50"
              onClick={handleToggle}
            />
          </>
        )}
      </AnimatePresence>
    </motion.nav>
  );
};

export default Navbar;
