import React, { useEffect, useRef } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { FaUsers, FaGraduationCap, FaAward, FaChartLine } from "react-icons/fa";
import { motion, useAnimation } from "framer-motion";
import aboutVector1 from "../assets/aboutPageBrv.png";
import aboutVector2 from "../assets/aboutbrv2.png";
import aboutVector3 from "../assets/aboutbrv3.png";
import aboutVector4 from "../assets/aboutbrv4.png";

gsap.registerPlugin(ScrollTrigger);

// Animation variants (remove "once: true" to allow re-trigger)
const fadeInLeft = {
  hidden: { opacity: 0, x: -80 },
  visible: { opacity: 1, x: 0, transition: { duration: 0.7, ease: "easeOut" } },
};
const fadeInRightImage = {
  hidden: { opacity: 0, x: 120, scale: 0.9, rotate: 8 },
  visible: {
    opacity: 1,
    x: 0,
    scale: 1,
    rotate: 0,
    transition: { duration: 1, ease: [0.22, 1, 0.36, 1] },
  },
};
const cardPop = {
  hidden: { opacity: 0, y: 60, scale: 0.92 },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: { type: "spring", stiffness: 320, damping: 24, duration: 0.64 },
  },
  whileHover: { scale: 1.06, boxShadow: "0 8px 32px rgba(80,80,200,0.15)" },
};
const stagger = {
  visible: {
    transition: {
      staggerChildren: 0.17,
      delayChildren: 0.2,
    },
  },
};

// Per-letter animation for heading
const letterVariants = {
  hidden: { opacity: 0, y: 40, scale: 0.85, rotate: -5 },
  visible: (i) => ({
    opacity: 1,
    y: 0,
    scale: 1,
    rotate: 0,
    transition: {
      delay: i * 0.04,
      duration: 0.5,
      type: "spring",
      stiffness: 500,
      damping: 24,
    },
  }),
};

// Word/line animation for subheadings and paragraph
const textStagger = {
  visible: {
    transition: {
      staggerChildren: 0.14,
      delayChildren: 0.17,
    },
  },
};
const wordVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: (i) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.08,
      duration: 0.5,
      type: "spring",
      stiffness: 400,
      damping: 24,
    },
  }),
};

function AnimatedHeading({ text, className }) {
  return (
    <motion.h2
      className={className}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: false, amount: 0.65 }} // <-- allows repeat
      aria-label={text}
      style={{ display: "flex", flexWrap: "wrap", lineHeight: 1.1 }}
    >
      {text.split("").map((char, i) =>
        char === " " ? (
          <span key={i} style={{ width: "0.4em" }} />
        ) : (
          <motion.span
            key={i}
            custom={i}
            variants={letterVariants}
            style={{ display: "inline-block" }}
          >
            {char}
          </motion.span>
        )
      )}
    </motion.h2>
  );
}

function AnimatedParagraph({ text, className }) {
  const words = text.split(" ");
  return (
    <motion.p
      className={className}
      variants={textStagger}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: false, amount: 0.6 }} // <-- allows repeat
      style={{ display: "flex", flexWrap: "wrap", lineHeight: 1.5 }}
    >
      {words.map((word, i) => (
        <motion.span
          key={i}
          custom={i}
          variants={wordVariants}
          style={{ marginRight: "0.3em", display: "inline-block" }}
        >
          {word}
        </motion.span>
      ))}
    </motion.p>
  );
}

const Feature = ({ icon, title, desc }) => (
  <motion.div
    className="bg-white border border-gray-100 rounded-2xl p-6 shadow-lg flex flex-col items-start text-left min-w-[160px] max-w-[220px] hover:shadow-2xl transition-transform"
    variants={cardPop}
    initial="hidden"
    whileInView="visible"
    whileHover="whileHover"
    viewport={{ once: false, amount: 0.2 }} // <-- allows repeat
  >
    <div className="mb-3 text-primary-600">{icon}</div>
    <div className="font-extrabold text-xl text-gray-900 mb-1">{title}</div>
    <div className="text-gray-500 text-base font-medium">{desc}</div>
  </motion.div>
);

const PanelContent = ({
  title,
  subtitle,
  description,
  children,
  image,
  imageAlt,
}) => (
  <motion.div
    className="grid md:grid-cols-2 gap-8 px-4 md:px-20 py-8 items-center w-full max-w-7xl mx-auto"
    initial="hidden"
    whileInView="visible"
    viewport={{ once: false, amount: 0.26 }} // <-- allows repeat
  >
    <motion.div
      className="space-y-7 text-left"
      variants={fadeInLeft}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: false, amount: 0.33 }} // <-- allows repeat
    >
      <AnimatedHeading
        text={title}
        className="text-4xl md:text-5xl font-extrabold text-gray-900 tracking-tight font-sans mb-2"
      />
      {subtitle && (
        <motion.h3
          className="text-2xl font-semibold text-primary-600"
          variants={wordVariants}
        >
          {subtitle}
        </motion.h3>
      )}
      {description && (
        <AnimatedParagraph
          text={description}
          className="text-lg text-gray-700 max-w-xl font-medium"
        />
      )}
      {children && (
        <motion.div
          className="mt-4 flex flex-wrap gap-5"
          variants={stagger}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: false, amount: 0.2 }} // <-- allows repeat
        >
          {children}
        </motion.div>
      )}
    </motion.div>
    <motion.div
      className="flex justify-center md:justify-end"
      variants={fadeInRightImage}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: false, amount: 0.33 }} // <-- allows repeat
      whileHover={{
        scale: 1.03,
        rotate: 2,
        boxShadow: "0 10px 40px 8px rgba(60,70,180,0.10)",
      }}
      transition={{ type: "spring", stiffness: 120, damping: 18 }}
    >
      <motion.img
        src={image}
        alt={imageAlt}
        className="w-64 md:w-96 max-h-[410px] object-contain rounded-2xl shadow-xl border border-gray-200"
        initial={{ opacity: 0, scale: 0.9, rotate: 8, y: 60 }}
        whileInView={{
          opacity: 1,
          scale: 1,
          rotate: 0,
          y: 0,
          transition: { duration: 1, ease: [0.22, 1, 0.36, 1] },
        }}
        whileHover={{ scale: 1.07, rotate: 2 }}
        viewport={{ once: false, amount: 0.2 }} // <-- allows repeat
      />
    </motion.div>
  </motion.div>
);

const PANELS = [
  {
    title: "About Proyuj",
    description:
      "Transforming careers through innovative interview preparation and professional development.",
    image: aboutVector1,
    imageAlt: "About Proyuj",
  },
  {
    title: "Our Impact in Number",
    description: "",
    image: aboutVector2,
    imageAlt: "Our Impact Vector",
    children: (
      <>
        <Feature
          icon={<FaUsers className="text-2xl text-primary-600" />}
          title="10K+"
          desc="Students"
        />
        <Feature
          icon={<FaAward className="text-2xl text-primary-600" />}
          title="95%"
          desc="Success Rate"
        />
        <Feature
          icon={<FaChartLine className="text-2xl text-primary-600" />}
          title="500+"
          desc="Partners"
        />
      </>
    ),
  },
  {
    title: "Features",
    description: "What makes Proyuj different from others in the market.",
    image: aboutVector3,
    imageAlt: "Features Vector",
    children: (
      <>
        <Feature
          icon={<FaUsers className="text-primary-600 text-2xl" />}
          title="Expert Network"
          desc="Industry professionals and certified interviewers"
        />
        <Feature
          icon={<FaGraduationCap className="text-primary-600 text-2xl" />}
          title="Proven Methods"
          desc="Research-backed techniques and strategies"
        />
        <Feature
          icon={<FaAward className="text-primary-600 text-2xl" />}
          title="Certification"
          desc="Industry-recognized credentials and achievements"
        />
        <Feature
          icon={<FaChartLine className="text-primary-600 text-2xl" />}
          title="Track Progress"
          desc="Detailed analytics and performance insights"
        />
      </>
    ),
  },
  {
    title: "Our Core Values",
    description: "Excellence, Community, Innovation.",
    image: aboutVector4,
    imageAlt: "Core Values Vector",
  },
];

const About = () => {
  const containerRef = useRef();

  useEffect(() => {
    const panels = gsap.utils.toArray(".about-panel");
    let ctx = gsap.context(() => {
      let scrollTween = gsap.to(panels, {
        xPercent: -100 * (panels.length - 1),
        ease: "power2.inOut",
        scrollTrigger: {
          trigger: containerRef.current,
          pin: true,
          scrub: 0.8, // slightly smoother
          anticipatePin: 1,
          end: () =>
            "+=" + containerRef.current.offsetWidth * (panels.length - 1),
        },
      });

      return () => {
        ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
      };
    }, containerRef);

    return () => ctx.revert();
  }, []);

  return (
    <section className="relative min-h-screen overflow-hidden bg-white font-sans">
      <div
        ref={containerRef}
        className="about-horizontal-container flex w-[400vw] h-screen"
      >
        {PANELS.map((panel, idx) => (
          <section
            key={idx}
            className="about-panel w-screen h-screen flex items-center justify-center bg-white px-2"
          >
            <PanelContent
              title={panel.title}
              subtitle={panel.subtitle}
              description={panel.description}
              children={panel.children}
              image={panel.image}
              imageAlt={panel.imageAlt}
            />
          </section>
        ))}
      </div>
    </section>
  );
};

export default About;
