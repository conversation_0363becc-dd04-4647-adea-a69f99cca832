import React from "react";

const CTA = () => (
  <section
    className="relative py-24 px-6 bg-gradient-to-br from-[#1C1F2A] via-[#23263A] to-[#29354d] text-white text-center overflow-hidden"
    id="cta"
  >
    {/* Decorative Glowing Circles */}
    <div className="pointer-events-none absolute top-0 left-0 w-full h-full z-0">
      <div className="absolute -top-24 -left-24 w-80 h-80 rounded-full bg-[#FCA311]/20 blur-3xl opacity-70 animate-pulse" />
      <div className="absolute bottom-0 right-0 w-64 h-64 rounded-full bg-[#61DAFB]/10 blur-2xl opacity-50" />
    </div>

    <div className="relative z-10 max-w-3xl mx-auto">
      <h3 className="text-4xl md:text-5xl font-extrabold leading-tight mb-8 bg-gradient-to-r from-[#FCA311] via-[#FF7E5F] to-[#FEB47B] bg-clip-text text-transparent drop-shadow-2xl">
        Ready to Ace Your Next Interview?
      </h3>
      <p className="text-lg md:text-xl text-[#EDEDED] mb-10 font-medium">
        Join <span className="text-[#FCA311] font-semibold">Proyuj</span> today
        and take a confident step toward your dream job.
      </p>
      <button className="bg-gradient-to-r from-[#FCA311] via-[#FF7E5F] to-[#FEB47B] text-[#1C1F2A] px-10 py-4 rounded-full font-bold text-xl shadow-xl hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-[#FCA311]/40">
        Book a Mock Interview Now
      </button>
    </div>
  </section>
);

export default CTA;
