import React, { useState } from 'react';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';
import useCompanyStore from '../../../store/companyStore';

const TestAPIDemo = () => {
  const [testId, setTestId] = useState('');
  const [questionIds, setQuestionIds] = useState('');
  const [candidateIds, setCandidateIds] = useState('');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState(null);

  const {
    addQuestionsToTest,
    assignCandidatesToTest,
    getCandidates,
    getQuestionCategories,
    filterQuestions,
    createQuestionBundle,
    getQuestionBundles,
    bulkAssignCandidatesToTests,
    bulkAddQuestionsToTests,
    bulkCreateQuestionBundlesByCategory,
    getTestStatusInfo,
    validateTest,
    validateBundle
  } = useCompanyStore();

  const testAddQuestions = async () => {
    if (!testId || !questionIds) {
      toast.error('Please provide test ID and question IDs');
      return;
    }

    setLoading(true);
    try {
      const questions = questionIds.split(',').map(id => ({
        questionId: id.trim(),
        points: 1
      }));

      const result = await addQuestionsToTest(testId, questions);
      setResults(result);
      
      if (result) {
        toast.success('Questions added successfully!');
      } else {
        toast.error('Failed to add questions');
      }
    } catch (error) {
      toast.error('Error adding questions');
    } finally {
      setLoading(false);
    }
  };

  const testAssignCandidates = async () => {
    if (!testId || !candidateIds) {
      toast.error('Please provide test ID and candidate IDs');
      return;
    }

    setLoading(true);
    try {
      const candidates = candidateIds.split(',').map(id => id.trim());
      const result = await assignCandidatesToTest(testId, candidates);
      setResults(result);
      
      if (result) {
        toast.success('Candidates assigned successfully!');
      } else {
        toast.error('Failed to assign candidates');
      }
    } catch (error) {
      toast.error('Error assigning candidates');
    } finally {
      setLoading(false);
    }
  };

  const testGetCandidates = async () => {
    setLoading(true);
    try {
      const result = await getCandidates({ page: 1, limit: 10 });
      setResults(result);
      toast.success('Candidates fetched successfully!');
    } catch (error) {
      toast.error('Error fetching candidates');
    } finally {
      setLoading(false);
    }
  };

  const testGetCategories = async () => {
    setLoading(true);
    try {
      const result = await getQuestionCategories();
      setResults(result);
      toast.success('Categories fetched successfully!');
    } catch (error) {
      toast.error('Error fetching categories');
    } finally {
      setLoading(false);
    }
  };

  const testFilterQuestions = async () => {
    setLoading(true);
    try {
      const result = await filterQuestions({
        searchTerm: 'javascript',
        category: 'Frontend',
        difficulty: 'Medium',
        page: 1,
        limit: 10
      });
      setResults(result);
      toast.success('Questions filtered successfully!');
    } catch (error) {
      toast.error('Error filtering questions');
    } finally {
      setLoading(false);
    }
  };

  const testCreateBundle = async () => {
    if (!questionIds) {
      toast.error('Please provide question IDs');
      return;
    }

    setLoading(true);
    try {
      const bundleData = {
        bundleName: 'Test Bundle',
        description: 'A test bundle created via API demo',
        category: 'Frontend',
        difficulty: 'Medium',
        questionIds: questionIds.split(',').map(id => id.trim()),
        tags: ['test', 'demo']
      };

      const result = await createQuestionBundle(bundleData);
      setResults(result);
      
      if (result) {
        toast.success('Bundle created successfully!');
      } else {
        toast.error('Failed to create bundle');
      }
    } catch (error) {
      toast.error('Error creating bundle');
    } finally {
      setLoading(false);
    }
  };

  const testGetBundles = async () => {
    setLoading(true);
    try {
      const result = await getQuestionBundles({ page: 1, limit: 10 });
      setResults(result);
      toast.success('Bundles fetched successfully!');
    } catch (error) {
      toast.error('Error fetching bundles');
    } finally {
      setLoading(false);
    }
  };

  const testBulkOperations = async () => {
    if (!testId || !questionIds || !candidateIds) {
      toast.error('Please provide all required IDs for bulk operations');
      return;
    }

    setLoading(true);
    try {
      const testIds = testId.split(',').map(id => id.trim());
      const questions = questionIds.split(',').map(id => ({
        questionId: id.trim(),
        points: 1
      }));
      const candidates = candidateIds.split(',').map(id => id.trim());

      // Test bulk candidate assignment
      const bulkCandidateResult = await bulkAssignCandidatesToTests(testIds, candidates);

      // Test bulk question assignment
      const bulkQuestionResult = await bulkAddQuestionsToTests(testIds, questions);

      setResults({
        bulkCandidates: bulkCandidateResult,
        bulkQuestions: bulkQuestionResult
      });

      toast.success('Bulk operations completed!');
    } catch (error) {
      toast.error('Error in bulk operations');
    } finally {
      setLoading(false);
    }
  };

  const testValidation = async () => {
    const testData = {
      testName: 'Sample Test',
      description: 'Test description',
      duration: 60,
      passingScore: 70,
      scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      endDate: new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString(),
      questions: [],
      associatedJobs: ['job1'],
      instructions: 'Test instructions',
      allowedAttempts: 1,
      randomizeQuestions: true,
      showResults: false
    };

    const bundleData = {
      bundleName: 'Test Bundle',
      description: 'Test bundle description',
      category: 'Frontend',
      difficulty: 'Medium',
      questionIds: questionIds ? questionIds.split(',').map(id => id.trim()) : ['q1', 'q2'],
      tags: ['test', 'demo']
    };

    const testValidation = validateTest(testData);
    const bundleValidation = validateBundle(bundleData);

    setResults({
      testValidation,
      bundleValidation
    });

    if (testValidation.isValid && bundleValidation.isValid) {
      toast.success('All validations passed!');
    } else {
      toast.error('Validation failed - check results');
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg"
    >
      <h2 className="text-2xl font-bold text-gray-800 mb-6">Test Management API Demo</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Input Fields */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Test ID</label>
            <input
              type="text"
              value={testId}
              onChange={(e) => setTestId(e.target.value)}
              placeholder="Enter test ID"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Question IDs (comma-separated)</label>
            <input
              type="text"
              value={questionIds}
              onChange={(e) => setQuestionIds(e.target.value)}
              placeholder="q1, q2, q3"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Candidate IDs (comma-separated)</label>
            <input
              type="text"
              value={candidateIds}
              onChange={(e) => setCandidateIds(e.target.value)}
              placeholder="c1, c2, c3"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={testAddQuestions}
            disabled={loading}
            className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg disabled:opacity-50"
          >
            Test Add Questions to Test
          </button>
          
          <button
            onClick={testAssignCandidates}
            disabled={loading}
            className="w-full px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg disabled:opacity-50"
          >
            Test Assign Candidates
          </button>
          
          <button
            onClick={testGetCandidates}
            disabled={loading}
            className="w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg disabled:opacity-50"
          >
            Test Get Candidates
          </button>
          
          <button
            onClick={testGetCategories}
            disabled={loading}
            className="w-full px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg disabled:opacity-50"
          >
            Test Get Categories
          </button>
          
          <button
            onClick={testFilterQuestions}
            disabled={loading}
            className="w-full px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg disabled:opacity-50"
          >
            Test Filter Questions
          </button>
          
          <button
            onClick={testCreateBundle}
            disabled={loading}
            className="w-full px-4 py-2 bg-pink-600 hover:bg-pink-700 text-white rounded-lg disabled:opacity-50"
          >
            Test Create Bundle
          </button>
          
          <button
            onClick={testGetBundles}
            disabled={loading}
            className="w-full px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg disabled:opacity-50"
          >
            Test Get Bundles
          </button>

          <button
            onClick={testBulkOperations}
            disabled={loading}
            className="w-full px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg disabled:opacity-50"
          >
            Test Bulk Operations
          </button>

          <button
            onClick={testValidation}
            disabled={loading}
            className="w-full px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg disabled:opacity-50"
          >
            Test Validation
          </button>
        </div>
      </div>

      {/* Results Display */}
      {results && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">API Response:</h3>
          <pre className="bg-gray-100 p-4 rounded-lg overflow-auto max-h-96 text-sm">
            {JSON.stringify(results, null, 2)}
          </pre>
        </div>
      )}
      
      {loading && (
        <div className="mt-6 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent mx-auto"></div>
          <p className="mt-2 text-gray-600">Testing API...</p>
        </div>
      )}
    </motion.div>
  );
};

export default TestAPIDemo;
