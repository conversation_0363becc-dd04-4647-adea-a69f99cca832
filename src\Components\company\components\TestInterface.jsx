import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  PlayIcon,
  PauseIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  TrophyIcon
} from '@heroicons/react/24/outline';

const TestInterface = ({ 
  questions = [], 
  selectedJob,
  onTestComplete,
  timePerQuestion = 60,
  showResults = true
}) => {
  const [testState, setTestState] = useState('not-started'); // 'not-started', 'running', 'paused', 'finished'
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [timeRemaining, setTimeRemaining] = useState(timePerQuestion);
  const [selectedAnswers, setSelectedAnswers] = useState({});
  const [answerResults, setAnswerResults] = useState({});
  const [testResults, setTestResults] = useState(null);

  const currentQuestion = questions[currentQuestionIndex];
  const totalQuestions = questions.length;
  const isLastQuestion = currentQuestionIndex === totalQuestions - 1;

  // Timer effect
  useEffect(() => {
    if (testState !== 'running' || totalQuestions === 0) return;

    if (timeRemaining === 0) {
      handleTimeUp();
      return;
    }

    const timer = setInterval(() => {
      setTimeRemaining(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [testState, timeRemaining, totalQuestions]);

  const handleTimeUp = useCallback(() => {
    if (isLastQuestion) {
      finishTest();
    } else {
      nextQuestion();
    }
  }, [isLastQuestion]);

  const startTest = () => {
    setTestState('running');
    setCurrentQuestionIndex(0);
    setTimeRemaining(timePerQuestion);
    setSelectedAnswers({});
    setAnswerResults({});
    setTestResults(null);
  };

  const pauseTest = () => {
    setTestState('paused');
  };

  const resumeTest = () => {
    setTestState('running');
  };

  const handleAnswerSelect = (optionIndex) => {
    if (testState !== 'running' || selectedAnswers[currentQuestionIndex] !== undefined) return;

    const optionValue = currentQuestion[`option${optionIndex + 1}`];
    const correctAnswer = currentQuestion.correctAnswer || currentQuestion.answer;
    const isCorrect = optionValue?.toLowerCase().trim() === correctAnswer?.toLowerCase().trim();

    setSelectedAnswers(prev => ({
      ...prev,
      [currentQuestionIndex]: optionIndex
    }));

    setAnswerResults(prev => ({
      ...prev,
      [currentQuestionIndex]: isCorrect
    }));
  };

  const nextQuestion = () => {
    if (currentQuestionIndex < totalQuestions - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
      setTimeRemaining(timePerQuestion);
    } else {
      finishTest();
    }
  };

  const previousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
      setTimeRemaining(timePerQuestion);
    }
  };

  const finishTest = () => {
    setTestState('finished');
    
    const correctAnswers = Object.values(answerResults).filter(Boolean).length;
    const totalAnswered = Object.keys(selectedAnswers).length;
    const score = totalAnswered > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;
    
    const results = {
      totalQuestions,
      totalAnswered,
      correctAnswers,
      incorrectAnswers: totalAnswered - correctAnswers,
      unanswered: totalQuestions - totalAnswered,
      score,
      timePerQuestion,
      selectedAnswers,
      answerResults
    };
    
    setTestResults(results);
    onTestComplete?.(results);
  };

  const restartTest = () => {
    setTestState('not-started');
    setCurrentQuestionIndex(0);
    setTimeRemaining(timePerQuestion);
    setSelectedAnswers({});
    setAnswerResults({});
    setTestResults(null);
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getOptionLetter = (index) => {
    return String.fromCharCode(65 + index); // A, B, C, D
  };

  if (totalQuestions === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-gray-500">
        <div className="text-6xl mb-4">📝</div>
        <h3 className="text-xl font-semibold mb-2">No Questions Available</h3>
        <p className="text-center">
          Please add some questions for {selectedJob?.title || 'this job'} before starting the test.
        </p>
      </div>
    );
  }

  // Not Started State
  if (testState === 'not-started') {
    return (
      <motion.div
        className="w-full max-w-2xl mx-auto text-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <div className="mb-6">
            <h2 className="text-3xl font-bold text-gray-800 mb-2">
              Ready to Start Test?
            </h2>
            <p className="text-gray-600">
              Test for: <span className="font-semibold">{selectedJob?.title}</span>
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-blue-600">{totalQuestions}</div>
              <div className="text-sm text-blue-800">Questions</div>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-green-600">{timePerQuestion}s</div>
              <div className="text-sm text-green-800">Per Question</div>
            </div>
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-purple-600">{Math.ceil((totalQuestions * timePerQuestion) / 60)}m</div>
              <div className="text-sm text-purple-800">Total Time</div>
            </div>
          </div>

          <button
            onClick={startTest}
            className="flex items-center gap-3 px-8 py-4 bg-[rgb(35,65,75)] text-white rounded-xl font-bold text-lg shadow-lg hover:bg-[rgb(45,85,100)] transition-all mx-auto"
          >
            <PlayIcon className="h-6 w-6" />
            Start Test
          </button>
        </div>
      </motion.div>
    );
  }

  // Test Running/Paused State
  if (testState === 'running' || testState === 'paused') {
    const questionText = currentQuestion?.questionText || currentQuestion?.question;
    
    return (
      <motion.div
        className="w-full max-w-3xl mx-auto"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <span className="text-lg font-semibold text-blue-700">
              Question {currentQuestionIndex + 1} of {totalQuestions}
            </span>
            <div className="w-48 bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentQuestionIndex + 1) / totalQuestions) * 100}%` }}
              />
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <div className={`flex items-center gap-2 px-3 py-2 rounded-lg ${
              timeRemaining <= 10 ? 'bg-red-100 text-red-700' : 'bg-gray-100 text-gray-700'
            }`}>
              <ClockIcon className="h-4 w-4" />
              <span className="font-bold">{formatTime(timeRemaining)}</span>
            </div>
            
            <button
              onClick={testState === 'running' ? pauseTest : resumeTest}
              className="p-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              {testState === 'running' ? (
                <PauseIcon className="h-4 w-4" />
              ) : (
                <PlayIcon className="h-4 w-4" />
              )}
            </button>
          </div>
        </div>

        {/* Question Card */}
        <div className="bg-white rounded-2xl shadow-xl p-8 mb-6">
          <h3 className="text-xl font-semibold text-gray-800 mb-6">
            {questionText}
          </h3>

          <div className="space-y-3">
            {[0, 1, 2, 3].map((optionIndex) => {
              const optionText = currentQuestion[`option${optionIndex + 1}`];
              if (!optionText) return null;

              const isSelected = selectedAnswers[currentQuestionIndex] === optionIndex;
              const isCorrect = answerResults[currentQuestionIndex] === true && isSelected;
              const isIncorrect = answerResults[currentQuestionIndex] === false && isSelected;
              const isAnswered = selectedAnswers[currentQuestionIndex] !== undefined;

              return (
                <button
                  key={optionIndex}
                  onClick={() => handleAnswerSelect(optionIndex)}
                  disabled={isAnswered || testState === 'paused'}
                  className={`w-full text-left p-4 rounded-lg border-2 transition-all duration-200 ${
                    isCorrect
                      ? 'bg-green-100 border-green-400 text-green-800'
                      : isIncorrect
                      ? 'bg-red-100 border-red-400 text-red-800'
                      : isSelected
                      ? 'bg-blue-100 border-blue-400 text-blue-800'
                      : 'bg-gray-50 border-gray-200 hover:bg-blue-50 hover:border-blue-300'
                  } ${isAnswered || testState === 'paused' ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                      isCorrect
                        ? 'bg-green-200 text-green-800'
                        : isIncorrect
                        ? 'bg-red-200 text-red-800'
                        : isSelected
                        ? 'bg-blue-200 text-blue-800'
                        : 'bg-gray-200 text-gray-600'
                    }`}>
                      {getOptionLetter(optionIndex)}
                    </div>
                    <span className="flex-1">{optionText}</span>
                    {isCorrect && <CheckCircleIcon className="h-5 w-5 text-green-600" />}
                    {isIncorrect && <XCircleIcon className="h-5 w-5 text-red-600" />}
                  </div>
                </button>
              );
            })}
          </div>

          {/* Answer Feedback */}
          {selectedAnswers[currentQuestionIndex] !== undefined && showResults && (
            <div className={`mt-4 p-3 rounded-lg ${
              answerResults[currentQuestionIndex] 
                ? 'bg-green-50 text-green-800' 
                : 'bg-red-50 text-red-800'
            }`}>
              <div className="font-semibold">
                {answerResults[currentQuestionIndex] ? '✅ Correct!' : '❌ Incorrect!'}
              </div>
              {!answerResults[currentQuestionIndex] && (
                <div className="text-sm mt-1">
                  Correct answer: {currentQuestion.correctAnswer || currentQuestion.answer}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between">
          <button
            onClick={previousQuestion}
            disabled={currentQuestionIndex === 0}
            className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ArrowLeftIcon className="h-4 w-4" />
            Previous
          </button>

          {selectedAnswers[currentQuestionIndex] !== undefined && (
            <button
              onClick={isLastQuestion ? finishTest : nextQuestion}
              className="flex items-center gap-2 px-6 py-2 bg-[rgb(35,65,75)] text-white rounded-lg hover:bg-[rgb(45,85,100)] transition-colors"
            >
              {isLastQuestion ? 'Finish Test' : 'Next'}
              {!isLastQuestion && <ArrowRightIcon className="h-4 w-4" />}
            </button>
          )}
        </div>
      </motion.div>
    );
  }

  // Test Finished State
  if (testState === 'finished' && testResults) {
    return (
      <motion.div
        className="w-full max-w-2xl mx-auto text-center"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
      >
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <div className="mb-6">
            <TrophyIcon className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-3xl font-bold text-gray-800 mb-2">Test Complete!</h2>
            <p className="text-gray-600">Here are your results:</p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-blue-600">{testResults.score}%</div>
              <div className="text-sm text-blue-800">Score</div>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-green-600">{testResults.correctAnswers}</div>
              <div className="text-sm text-green-800">Correct</div>
            </div>
            <div className="bg-red-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-red-600">{testResults.incorrectAnswers}</div>
              <div className="text-sm text-red-800">Incorrect</div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-gray-600">{testResults.unanswered}</div>
              <div className="text-sm text-gray-800">Unanswered</div>
            </div>
          </div>

          <button
            onClick={restartTest}
            className="px-8 py-3 bg-[rgb(35,65,75)] text-white rounded-xl font-bold shadow-lg hover:bg-[rgb(45,85,100)] transition-all"
          >
            Take Test Again
          </button>
        </div>
      </motion.div>
    );
  }

  return null;
};

export default TestInterface;
