import React, { useEffect, useRef, useState } from 'react';
import { Mail, Shield, Clock, RefreshCw, CheckCircle } from 'lucide-react';

const VerifyOtp = () => {
    const [formData, setFormData] = useState({ email: '', code: '' });
    const [message, setMessage] = useState('');
    const [error, setError] = useState('');
    const [timer, setTimer] = useState(60);
    const [resending, setResending] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [focusedField, setFocusedField] = useState('');
    const [otpDigits, setOtpDigits] = useState(['', '', '', '', '', '']);

    const otpRefs = useRef([]);

    useEffect(() => {
        otpRefs.current[0]?.focus(); // Auto-focus first OTP field on mount
    }, []);

    useEffect(() => {
        if (timer === 0) return;
        const interval = setInterval(() => setTimer((t) => t - 1), 1000);
        return () => clearInterval(interval);
    }, [timer]);

    const handleChange = (e) => {
        setFormData((prev) => ({ ...prev, [e.target.name]: e.target.value }));
    };

    const handleOtpChange = (index, value) => {
        if (!/^\d*$/.test(value)) return;

        const newDigits = [...otpDigits];
        newDigits[index] = value;
        setOtpDigits(newDigits);
        setFormData((prev) => ({ ...prev, code: newDigits.join('') }));

        if (value && index < 5) otpRefs.current[index + 1]?.focus();
    };

    const handleOtpKeyDown = (index, e) => {
        if (e.key === 'Backspace' && !otpDigits[index] && index > 0) {
            otpRefs.current[index - 1]?.focus();
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setMessage('');
        setError('');
        setIsSubmitting(true);
        try {
            const res = await fetch('/api/auth/verify-otp', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formData),
            });
            const data = await res.json();
            if (!res.ok) throw new Error(data.error || 'OTP verification failed');
            setMessage(data.message);
        } catch (err) {
            setError(err.message);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleResendOtp = async () => {
        setResending(true);
        setError('');
        setMessage('');
        try {
            const res = await fetch('/api/auth/resend-otp', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email: formData.email }),
            });
            const data = await res.json();
            if (!res.ok) throw new Error(data.error || 'Failed to resend OTP');

            setMessage('OTP resent successfully.');
            setTimer(60);
            setOtpDigits(['', '', '', '', '', '']);
            setFormData((prev) => ({ ...prev, code: '' }));
            otpRefs.current[0]?.focus();
        } catch (err) {
            setError(err.message);
        } finally {
            setResending(false);
        }
    };

    const formatTime = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-900 via-cyan-900 to-teal-800 flex items-center justify-center p-4 relative overflow-hidden">

            {/* Background Blobs */}
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
            <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-teal-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-cyan-500 rounded-full mix-blend-multiply filter blur-xl opacity-50 animate-pulse"></div>

            <div className="relative w-full max-w-lg">
                <div className="bg-white bg-opacity-10 backdrop-blur-xl border border-white border-opacity-20 rounded-3xl p-8 shadow-2xl">

                    <div className="text-center mb-8">
                        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-cyan-500 to-teal-500 rounded-full mb-6 shadow-2xl">
                            <Shield className="w-10 h-10 text-white" />
                        </div>
                        <h1 className="text-4xl font-bold text-white mb-3 bg-gradient-to-r from-teal-200 via-white to-cyan-200 bg-clip-text text-transparent">
                            Verify Your Identity
                        </h1>
                        <p className="text-blue-100 text-sm opacity-80 mb-4">
                            We've sent a 6-digit code to your email
                        </p>
                        <div className="w-24 h-1 bg-gradient-to-r from-cyan-400 to-teal-400 rounded-full mx-auto opacity-60"></div>
                    </div>

                    <div className="space-y-6">
                        {/* Email */}
                        <div className="group">
                            <label htmlFor="email" className="block text-sm font-semibold text-cyan-100 mb-3">
                                Email Address
                            </label>
                            <div className="relative">
                                <Mail className={`absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 ${focusedField === 'email' ? 'text-cyan-300 scale-110' : 'text-cyan-400'} transition-all`} />
                                <input
                                    id="email"
                                    name="email"
                                    type="email"
                                    value={formData.email}
                                    onChange={handleChange}
                                    onFocus={() => setFocusedField('email')}
                                    onBlur={() => setFocusedField('')}
                                    required
                                    className="w-full bg-white bg-opacity-10 border border-white border-opacity-20 pl-14 pr-4 py-4 rounded-2xl text-white placeholder-cyan-300 focus:outline-none focus:border-cyan-400 focus:bg-opacity-20 transition-all hover:bg-opacity-15"
                                    placeholder="Enter your email address"
                                />
                            </div>
                        </div>

                        {/* OTP Inputs */}
                        <div>
                            <label className="block text-sm font-semibold text-cyan-100 mb-3">Enter 6-Digit Code</label>
                            <div className="flex justify-center space-x-3 mb-4">
                                {otpDigits.map((digit, index) => (
                                    <input
                                        key={index}
                                        ref={(el) => (otpRefs.current[index] = el)}
                                        type="text"
                                        maxLength={1}
                                        value={digit}
                                        onChange={(e) => handleOtpChange(index, e.target.value)}
                                        onKeyDown={(e) => handleOtpKeyDown(index, e)}
                                        className="w-12 h-14 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white text-center text-xl font-bold focus:outline-none focus:border-cyan-400 focus:ring-2 focus:ring-cyan-500 hover:scale-105 transition-all"
                                    />
                                ))}
                            </div>
                            <p className="text-cyan-200 text-xs text-center opacity-70">Enter the code exactly as received</p>
                        </div>

                        {/* Timer and Resend */}
                        <div className="flex items-center justify-center mb-6">
                            {timer > 0 ? (
                                <div className="flex items-center space-x-2 text-cyan-200">
                                    <Clock className="w-4 h-4" />
                                    <span className="text-sm font-medium">Resend code in {formatTime(timer)}</span>
                                </div>
                            ) : (
                                <button
                                    type="button"
                                    onClick={handleResendOtp}
                                    disabled={resending}
                                    className="flex items-center space-x-2 text-cyan-300 hover:text-white transition disabled:opacity-50 disabled:cursor-not-allowed group"
                                >
                                    <RefreshCw className={`w-4 h-4 ${resending ? 'animate-spin' : 'group-hover:rotate-180'} transition`} />
                                    <span className="text-sm font-medium underline underline-offset-4">
                                        {resending ? 'Sending...' : 'Resend Code'}
                                    </span>
                                </button>
                            )}
                        </div>

                        {/* Submit Button */}
                        <button
                            type="button"
                            onClick={handleSubmit}
                            disabled={isSubmitting || formData.code.length !== 6}
                            className="w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white font-semibold py-4 px-6 rounded-2xl shadow-xl hover:shadow-2xl disabled:opacity-60 transition-all focus:ring-4 focus:ring-cyan-500"
                        >
                            <span className="flex items-center justify-center">
                                {isSubmitting ? (
                                    <>
                                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                                        Verifying...
                                    </>
                                ) : (
                                    <>
                                        <CheckCircle className="w-5 h-5 mr-2" />
                                        Verify Code
                                    </>
                                )}
                            </span>
                        </button>
                    </div>

                    {/* Message Displays */}
                    {message && (
                        <div className="mt-6 p-4 bg-green-500 bg-opacity-20 border border-green-500 border-opacity-30 rounded-xl text-green-200 text-sm text-center">
                            <CheckCircle className="w-4 h-4 inline-block mr-1" />
                            {message}
                        </div>
                    )}
                    {error && (
                        <div className="mt-6 p-4 bg-red-500 bg-opacity-20 border border-red-500 border-opacity-30 rounded-xl text-red-200 text-sm text-center">
                            {error}
                        </div>
                    )}

                    <p className="mt-8 text-cyan-200 text-xs text-center opacity-70">
                        Didn’t receive the code? Check your spam or try resending.
                    </p>
                </div>
            </div>
        </div>
    );
};

export default VerifyOtp;
