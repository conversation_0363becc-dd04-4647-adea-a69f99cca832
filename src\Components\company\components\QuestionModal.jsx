import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  XMarkIcon,
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  ClockIcon,
  TagIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

const QuestionModal = ({ 
  isOpen, 
  onClose, 
  question, 
  onEdit, 
  onDelete,
  showActions = true 
}) => {
  const [showAnswer, setShowAnswer] = useState(false);

  if (!question) return null;

  const handleEdit = () => {
    onEdit?.(question);
    onClose();
  };

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this question?')) {
      onDelete?.(question._id || question.id);
      onClose();
    }
  };

  const getOptionLetter = (index) => {
    return String.fromCharCode(65 + index); // A, B, C, D
  };

  const isCorrectOption = (option) => {
    return option && option.isCorrect === true;
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={(e) => e.target === e.currentTarget && onClose()}
        >
          <motion.div
            className="bg-white rounded-2xl shadow-2xl w-full max-w-3xl h-[90vh] flex flex-col overflow-hidden"
            initial={{ scale: 0.9, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.9, opacity: 0, y: 20 }}
            transition={{ duration: 0.3, type: 'spring', stiffness: 300, damping: 30 }}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-[rgb(35,65,75)] to-gray-700">
              <div className="flex items-center gap-3">
                <DocumentTextIcon className="h-6 w-6 text-white" />
                <h2 className="text-xl font-bold text-white">Question Details</h2>
              </div>
              <div className="flex items-center gap-2">
                {showActions && (
                  <>
                    <button
                      onClick={handleEdit}
                      className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
                      title="Edit Question"
                    >
                      <PencilIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={handleDelete}
                      className="p-2 text-white hover:bg-red-500/20 rounded-lg transition-colors"
                      title="Delete Question"
                    >
                      <TrashIcon className="h-5 w-5" />
                    </button>
                  </>
                )}
                <button
                  onClick={onClose}
                  className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
                >
                  <XMarkIcon className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 p-6 overflow-y-auto min-h-0">
              {/* Question Metadata */}
              <div className="flex flex-wrap items-center gap-4 mb-6">
                <div className="flex items-center gap-2">
                  <TagIcon className="h-4 w-4 text-gray-600" />
                  <span className="px-3 py-1 bg-gray-200 text-gray-800 text-sm font-semibold rounded-full">
                    {question.category || 'No Category'}
                  </span>
                </div>

                <span className="px-3 py-1 bg-gray-200 text-gray-700 text-sm rounded-full">
                  {question.questionType || 'MCQ'}
                </span>

                <span className="px-3 py-1 bg-gray-200 text-gray-700 text-sm rounded-full">
                  {question.difficulty || 'Medium'}
                </span>

                {question.points && (
                  <span className="px-3 py-1 bg-[#fcb045] text-white text-sm rounded-full">
                    {question.points} points
                  </span>
                )}

                {question.createdAt && (
                  <div className="flex items-center gap-1 text-sm text-gray-500">
                    <ClockIcon className="h-4 w-4" />
                    <span>Created: {new Date(question.createdAt).toLocaleDateString()}</span>
                  </div>
                )}
              </div>

              {/* Question Text */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-3">Question:</h3>
                <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <p className="text-gray-800 leading-relaxed">
                    {question.questionText || question.question}
                  </p>
                </div>
              </div>

              {/* Options or Answer Display */}
              <div className="mb-6">
                {question.options && Array.isArray(question.options) ? (
                  <>
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">Options:</h3>
                    <div className="space-y-3">
                      {question.options.map((option, index) => {
                        if (!option.text) return null;

                        const isCorrect = isCorrectOption(option);

                        return (
                          <div
                            key={index}
                            className={`flex items-center gap-3 p-4 rounded-lg border transition-all ${
                              showAnswer && isCorrect
                                ? 'bg-green-50 border-green-200 ring-2 ring-green-200'
                                : 'bg-white border-gray-200 hover:border-gray-300'
                            }`}
                          >
                            <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-semibold ${
                              showAnswer && isCorrect
                                ? 'bg-[#fcb045] text-white'
                                : 'bg-gray-200 text-gray-600'
                            }`}>
                              {getOptionLetter(index)}
                            </div>
                            <span className="flex-1 text-gray-800">{option.text}</span>
                            {showAnswer && isCorrect && (
                              <CheckCircleIcon className="h-5 w-5 text-[#fcb045]" />
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </>
                ) : question.correctAnswer ? (
                  <>
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">Answer Type:</h3>
                    <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                      <span className="text-gray-600">
                        {question.questionType === 'Code' ? 'Code Response Required' : 'Text Response Required'}
                      </span>
                    </div>
                  </>
                ) : (
                  <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <span className="text-gray-500">No answer options available</span>
                  </div>
                )}
              </div>

              {/* Answer Section */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-800">
                    {question.options && Array.isArray(question.options) ? 'Correct Answer:' : 'Answer:'}
                  </h3>
                  <button
                    onClick={() => setShowAnswer(!showAnswer)}
                    className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                      showAnswer
                        ? 'bg-green-100 text-green-700 hover:bg-green-200'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {showAnswer ? 'Hide Answer' : 'Show Answer'}
                  </button>
                </div>

                <AnimatePresence>
                  {showAnswer && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="p-4 bg-green-50 border border-green-200 rounded-lg"
                    >
                      {question.options && Array.isArray(question.options) ? (
                        <div className="space-y-2">
                          {question.options
                            .filter(opt => opt.isCorrect)
                            .map((option, index) => (
                              <div key={index} className="flex items-center gap-2">
                                <CheckCircleIcon className="h-5 w-5 text-[#fcb045]" />
                                <span className="font-semibold text-gray-800">{option.text}</span>
                              </div>
                            ))}
                        </div>
                      ) : question.correctAnswer ? (
                        <div className="flex items-start gap-2">
                          <CheckCircleIcon className="h-5 w-5 text-[#fcb045] mt-0.5" />
                          <div className="flex-1">
                            <span className="font-semibold text-gray-800 block">
                              {question.correctAnswer}
                            </span>
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-500">No answer available</span>
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Explanation Section */}
              {question.explanation && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">Explanation:</h3>
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-blue-800">{question.explanation}</p>
                  </div>
                </div>
              )}

              {/* Additional Information */}
              <div className="border-t border-gray-200 pt-4">
                <h3 className="text-sm font-semibold text-gray-600 mb-2">Additional Information:</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                  {question.updatedAt && (
                    <div>
                      <span className="font-medium">Last Updated:</span>
                      <span className="ml-2">{new Date(question.updatedAt).toLocaleDateString()}</span>
                    </div>
                  )}
                  <div>
                    <span className="font-medium">Question Type:</span>
                    <span className="ml-2">{question.questionType || 'MCQ'}</span>
                  </div>
                  <div>
                    <span className="font-medium">Category:</span>
                    <span className="ml-2">{question.category || 'Not specified'}</span>
                  </div>
                  <div>
                    <span className="font-medium">Difficulty:</span>
                    <span className="ml-2 capitalize">{question.difficulty || 'Medium'}</span>
                  </div>
                  <div>
                    <span className="font-medium">Points:</span>
                    <span className="ml-2">{question.points || 1}</span>
                  </div>
                  <div>
                    <span className="font-medium">Status:</span>
                    <span className={`ml-2 ${question.isActive !== false ? 'text-[#fcb045]' : 'text-gray-600'}`}>
                      {question.isActive !== false ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  {question._id && (
                    <div className="md:col-span-2">
                      <span className="font-medium">ID:</span>
                      <span className="ml-2 font-mono text-xs">{question._id}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Footer */}
            {showActions && (
              <div className="flex-shrink-0 flex items-center justify-end gap-3 p-6 border-t border-gray-200 bg-gray-50">
                <button
                  onClick={onClose}
                  className="px-6 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Close
                </button>
                <button
                  onClick={handleEdit}
                  className="px-6 py-3 bg-gradient-to-r from-[rgb(35,65,75)] to-gray-900 text-white rounded-lg hover:from-[rgb(45,75,85)] hover:to-gray-800 transition-all min-h-[44px]"
                >
                  Edit Question
                </button>
              </div>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default QuestionModal;
