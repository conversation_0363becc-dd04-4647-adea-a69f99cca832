import React, { useState } from 'react';
import toast from 'react-hot-toast';
import {
    BriefcaseIcon,
    MapPinIcon,
    CalendarIcon,
    EyeIcon,
    PencilIcon,
    UserGroupIcon,
    ClockIcon,
    CheckCircleIcon,
    XCircleIcon,
    EllipsisVerticalIcon,
    ChartBarIcon,
    DocumentTextIcon
} from '@heroicons/react/24/outline';
import {
    CheckCircleIcon as CheckCircleSolid,
    XCircleIcon as XCircleSolid,
    ClockIcon as ClockSolid,
    FireIcon
} from '@heroicons/react/24/solid';
import { motion, AnimatePresence } from 'framer-motion';

const JobCard = ({ job, onEdit, onStatusUpdate, onViewApplications, onViewDetails }) => {
    const [showDropdown, setShowDropdown] = useState(false);
    const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
    const [statusUpdateSuccess, setStatusUpdateSuccess] = useState(false);

    const {
        title,
        location,
        jobType,
        createdAt,
        salary,
        description,
        companyName,
        skills,
        techStack,
        _id,
        isActive,
        applications = 0,
        maxApplications = 0,
        applicationDeadline,
        experienceLevel,
        workMode,
        category,
        hasTest,
        applicants
    } = job;

    const formatSalary = (salary) => {
        if (!salary || typeof salary !== 'object') return null;

        const { min, max, currency } = salary;
        const symbolMap = {
            INR: '₹',
            USD: '$',
            EUR: '€',
            GBP: '£'
        };

        const symbol = symbolMap[currency] || currency;
        const format = (val) => {
            if (val >= 100000) {
                return `${(val / 100000).toFixed(1)}L`;
            }
            return val.toLocaleString('en-IN', { maximumFractionDigits: 0 });
        };

        return `${symbol}${format(min)} - ${symbol}${format(max)}`;
    };

    const formatDate = (date) => {
        const now = new Date();
        const jobDate = new Date(date);
        const diffTime = Math.abs(now - jobDate);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) return 'Today';
        if (diffDays === 2) return 'Yesterday';
        if (diffDays <= 7) return `${diffDays} days ago`;
        return jobDate.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: jobDate.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
        });
    };

    const formatDeadline = (deadline) => {
        const now = new Date();
        const deadlineDate = new Date(deadline);
        const diffTime = deadlineDate - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays < 0) return { text: 'Expired', color: 'text-red-600', urgent: true };
        if (diffDays === 0) return { text: 'Today', color: 'text-red-600', urgent: true };
        if (diffDays === 1) return { text: '1 day left', color: 'text-orange-600', urgent: true };
        if (diffDays <= 7) return { text: `${diffDays} days left`, color: 'text-orange-600', urgent: false };
        return { text: `${diffDays} days left`, color: 'text-green-600', urgent: false };
    };

    const handleStatusUpdate = async (newStatus) => {
        setIsUpdatingStatus(true);

        // Show loading toast
        const loadingToast = toast.loading(`${newStatus ? 'Activating' : 'Deactivating'} job...`);

        try {
            await onStatusUpdate(_id, newStatus);
            setStatusUpdateSuccess(true);
            setTimeout(() => setStatusUpdateSuccess(false), 2000);

            // Show success toast
            toast.success(`Job ${newStatus ? 'activated' : 'deactivated'} successfully!`, {
                id: loadingToast,
                duration: 3000,
                icon: newStatus ? '✅' : '⏸️',
            });
        } catch (error) {
            console.error('Status update failed:', error);
            toast.error('Failed to update job status. Please try again.', {
                id: loadingToast,
                duration: 4000,
            });
        } finally {
            setIsUpdatingStatus(false);
            setShowDropdown(false);
        }
    };

    const getJobStatusInfo = () => {
        const deadline = applicationDeadline ? formatDeadline(applicationDeadline) : null;
        const isExpired = deadline?.text === 'Expired';
        const applicationsFull = applications >= maxApplications;

        if (!isActive) {
            return {
                status: 'Inactive',
                color: 'bg-gray-100 text-gray-700 border-gray-200',
                icon: XCircleSolid,
                iconColor: 'text-gray-500'
            };
        }

        if (isExpired || applicationsFull) {
            return {
                status: 'Closed',
                color: 'bg-red-100 text-red-700 border-red-200',
                icon: XCircleSolid,
                iconColor: 'text-red-500'
            };
        }

        if (deadline?.urgent) {
            return {
                status: 'Urgent',
                color: 'bg-orange-100 text-orange-700 border-orange-200',
                icon: ClockSolid,
                iconColor: 'text-orange-500'
            };
        }

        return {
            status: 'Active',
            color: 'bg-green-100 text-green-700 border-green-200',
            icon: CheckCircleSolid,
            iconColor: 'text-green-500'
        };
    };

    const statusInfo = getJobStatusInfo();
    const StatusIcon = statusInfo.icon;
    const applicationProgress = maxApplications ? (applicants.length / maxApplications) * 100 : 0;
    const skillsToShow = (skills || techStack || []).slice(0, 4);
    const hasMoreSkills = (skills || techStack || []).length > 4;

    return (
        <motion.div
            className="bg-white rounded-2xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-gray-300 transition-all duration-300 overflow-hidden group"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            whileHover={{ y: -2 }}
            layout
        >
            {/* Status Success Animation */}
            <AnimatePresence>
                {statusUpdateSuccess && (
                    <motion.div
                        className="absolute inset-0 bg-green-500 bg-opacity-10 flex items-center justify-center z-10"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                    >
                        <motion.div
                            className="bg-green-500 text-white px-4 py-2 rounded-lg flex items-center gap-2"
                            initial={{ scale: 0.8 }}
                            animate={{ scale: 1 }}
                            exit={{ scale: 0.8 }}
                        >
                            <CheckCircleIcon className="w-5 h-5" />
                            Status Updated!
                        </motion.div>
                    </motion.div>
                )}
            </AnimatePresence>

            {/* Header */}
            <div className="p-6 pb-4">
                <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-1">
                                {title}
                            </h3>
                            {hasTest && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-700">
                                    <DocumentTextIcon className="w-3 h-3 mr-1" />
                                    Test
                                </span>
                            )}
                        </div>
                        <div className="flex items-center justify-between">
                            <p className="text-sm text-gray-600 font-medium">{companyName}</p>
                            <div className="flex items-center gap-2">
                                <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${statusInfo.color}`}>
                                    <StatusIcon className={`w-3 h-3 mr-1 ${statusInfo.iconColor}`} />
                                    {statusInfo.status}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Actions Dropdown */}
                    <div className="relative ml-4">
                        <button
                            onClick={() => setShowDropdown(!showDropdown)}
                            className="p-2 rounded-lg hover:bg-gray-100 transition-colors opacity-0 group-hover:opacity-100"
                            disabled={isUpdatingStatus}
                        >
                            {isUpdatingStatus ? (
                                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                            ) : (
                                <EllipsisVerticalIcon className="w-5 h-5 text-gray-500" />
                            )}
                        </button>

                        <AnimatePresence>
                            {showDropdown && (
                                <motion.div
                                    className="absolute right-0 top-full mt-1 w-48 bg-white rounded-xl shadow-lg border border-gray-200 py-1 z-20"
                                    initial={{ opacity: 0, scale: 0.95, y: -10 }}
                                    animate={{ opacity: 1, scale: 1, y: 0 }}
                                    exit={{ opacity: 0, scale: 0.95, y: -10 }}
                                    transition={{ duration: 0.1 }}
                                >
                                    <button
                                        onClick={() => {
                                            onEdit(job);
                                            setShowDropdown(false);
                                        }}
                                        className="w-full flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                                    >
                                        <PencilIcon className="w-4 h-4" />
                                        Edit Job
                                    </button>

                                    <button
                                        onClick={() => {
                                            onViewApplications && onViewApplications(_id);
                                            setShowDropdown(false);
                                        }}
                                        className="w-full flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                                    >
                                        <UserGroupIcon className="w-4 h-4" />
                                        View Applications
                                    </button>

                                    <div className="border-t border-gray-100 my-1"></div>

                                    <button
                                        onClick={() => handleStatusUpdate(!isActive)}
                                        className={`w-full flex items-center gap-2 px-4 py-2 text-sm transition-colors ${isActive
                                            ? 'text-red-700 hover:bg-red-50'
                                            : 'text-green-700 hover:bg-green-50'
                                            }`}
                                    >
                                        {isActive ? (
                                            <>
                                                <XCircleIcon className="w-4 h-4" />
                                                Deactivate
                                            </>
                                        ) : (
                                            <>
                                                <CheckCircleIcon className="w-4 h-4" />
                                                Activate
                                            </>
                                        )}
                                    </button>
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </div>
                </div>

                {/* Job Info Grid */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
                    <div className="flex items-center gap-1 text-sm text-gray-600">
                        <MapPinIcon className="w-4 h-4 text-blue-500" />
                        <span className="truncate">{location}</span>
                    </div>

                    <div className="flex items-center gap-1 text-sm text-gray-600">
                        <BriefcaseIcon className="w-4 h-4 text-green-500" />
                        <span>{jobType}</span>
                    </div>

                    <div className="flex items-center gap-1 text-sm text-gray-600">
                        <CalendarIcon className="w-4 h-4 text-purple-500" />
                        <span>{formatDate(createdAt)}</span>
                    </div>

                    {experienceLevel && (
                        <div className="flex items-center gap-1 text-sm text-gray-600">
                            <ChartBarIcon className="w-4 h-4 text-orange-500" />
                            <span>{experienceLevel}</span>
                        </div>
                    )}
                </div>

                {/* Salary & Work Mode */}
                <div className="flex items-center justify-between mb-4">
                    {salary && (
                        <div className="flex items-center gap-2">
                            <div className="p-1.5 bg-green-100 rounded-lg">
                                <span className="text-green-600 text-lg">₹</span>
                            </div>
                            <div>
                                <p className="text-sm font-semibold text-gray-900">
                                    {formatSalary(salary)}
                                </p>
                                <p className="text-xs text-gray-500">per annum</p>
                            </div>
                        </div>
                    )}

                    {workMode && (
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${workMode === 'Remote' ? 'bg-blue-100 text-blue-700' :
                            workMode === 'Hybrid' ? 'bg-purple-100 text-purple-700' :
                                'bg-gray-100 text-gray-700'
                            }`}>
                            {workMode}
                        </span>
                    )}
                </div>

                {/* Skills */}
                {skillsToShow.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-4">
                        {skillsToShow.map((skill, index) => (
                            <span
                                key={index}
                                className="px-3 py-1 bg-gray-100 text-gray-700 rounded-lg text-xs font-medium hover:bg-gray-200 transition-colors"
                            >
                                {skill}
                            </span>
                        ))}
                        {hasMoreSkills && (
                            <span className="px-3 py-1 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 rounded-lg text-xs font-medium">
                                +{(skills || techStack || []).length - 4} more
                            </span>
                        )}
                    </div>
                )}

                {/* Description */}
                <p className="text-sm text-gray-600 line-clamp-2 mb-4">
                    {description}
                </p>
            </div>

            {/* Footer */}
            <div className="px-6 pb-6">
                {/* Application Progress */}
                <div className="mb-4">
                    <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-gray-700 flex items-center gap-1">
                            <UserGroupIcon className="w-4 h-4" />
                            Applications {applicants.length}
                        </span>
                        <span className="text-sm text-gray-600">
                            {applicants.length}{maxApplications ? `/${maxApplications}` : ''}
                        </span>
                    </div>

                    <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                        <motion.div
                            className={`h-full rounded-full transition-all duration-500 ${applicationProgress >= 90 ? 'bg-red-500' :
                                applicationProgress >= 70 ? 'bg-orange-500' :
                                    'bg-green-500'
                                }`}
                            initial={{ width: 0 }}
                            animate={{ width: `${Math.min(applicationProgress, 100)}%` }}
                            transition={{ duration: 1, delay: 0.2 }}
                        />
                    </div>
                </div>

                {/* Deadline & Actions */}
                <div className="flex items-center justify-between">
                    {applicationDeadline && (
                        <div className="flex items-center gap-1">
                            <ClockIcon className={`w-4 h-4 ${formatDeadline(applicationDeadline).color}`} />
                            <span className={`text-sm font-medium ${formatDeadline(applicationDeadline).color}`}>
                                {formatDeadline(applicationDeadline).text}
                            </span>
                            {formatDeadline(applicationDeadline).urgent && (
                                <FireIcon className="w-4 h-4 text-orange-500 animate-pulse" />
                            )}
                        </div>
                    )}

                    <div className="flex gap-2 ml-auto">
                        <button
                            onClick={() => onViewApplications && onViewApplications(_id)}
                            className="flex items-center gap-1 px-3 py-1.5 bg-blue-50 text-blue-600 rounded-lg text-sm font-medium hover:bg-blue-100 transition-colors"
                        >
                            <EyeIcon className="w-4 h-4" />
                            View
                        </button>

                        <button
                            onClick={() => onEdit(job)}
                            className="flex items-center gap-1 px-3 py-1.5 bg-gray-50 text-gray-600 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors"
                        >
                            <PencilIcon className="w-4 h-4" />
                            Edit
                        </button>
                    </div>
                </div>
            </div>

            {/* Overlay for click outside dropdown */}
            {showDropdown && (
                <div
                    className="fixed inset-0 z-10"
                    onClick={() => setShowDropdown(false)}
                />
            )}
        </motion.div>
    );
};

export default JobCard;