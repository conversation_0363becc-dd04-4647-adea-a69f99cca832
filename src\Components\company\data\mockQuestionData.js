// Mock data for demonstration purposes
export const questionCategories = [
  'JavaScript',
  'Python',
  'React',
  'Node.js',
  'Data Structures',
  'Algorithms',
  'Database',
  'SQL',
  'MongoDB',
  'Backend Development',
  'Frontend Development',
  'UI/UX Design',
  'DevOps',
  'AWS',
  'Docker',
  'Kubernetes',
  'System Design',
  'API Development',
  'Testing',
  'Security',
  'Machine Learning',
  'Data Science',
  'HTML/CSS',
  'TypeScript',
  'Vue.js',
  'Angular',
  'Express.js',
  'GraphQL',
  'REST API',
  'Microservices'
];

export const mockQuestions = [
  // JavaScript Questions
  {
    _id: 'q1',
    questionText: 'What is the difference between let, const, and var in JavaScript?',
    type: 'MCQ',
    difficulty: 'Medium',
    category: 'JavaScript',
    tags: ['variables', 'scope', 'hoisting'],
    options: [
      'let and const are block-scoped, var is function-scoped',
      'All three are function-scoped',
      'var and let are block-scoped, const is function-scoped',
      'All three are block-scoped'
    ],
    correctAnswer: 0,
    points: 2
  },
  {
    _id: 'q2',
    questionText: 'Explain the concept of closures in JavaScript with an example.',
    type: 'Short Answer',
    difficulty: 'Hard',
    category: 'JavaScript',
    tags: ['closures', 'scope', 'functions'],
    points: 3
  },
  {
    _id: 'q3',
    questionText: 'Write a function to reverse a string in JavaScript.',
    type: 'Coding',
    difficulty: 'Easy',
    category: 'JavaScript',
    tags: ['strings', 'algorithms'],
    points: 2
  },

  // Python Questions
  {
    _id: 'q4',
    questionText: 'What is the difference between a list and a tuple in Python?',
    type: 'MCQ',
    difficulty: 'Easy',
    category: 'Python',
    tags: ['data-structures', 'lists', 'tuples'],
    options: [
      'Lists are mutable, tuples are immutable',
      'Lists are immutable, tuples are mutable',
      'Both are mutable',
      'Both are immutable'
    ],
    correctAnswer: 0,
    points: 1
  },
  {
    _id: 'q5',
    questionText: 'Implement a Python function to find the factorial of a number using recursion.',
    type: 'Coding',
    difficulty: 'Medium',
    category: 'Python',
    tags: ['recursion', 'algorithms', 'mathematics'],
    points: 3
  },

  // React Questions
  {
    _id: 'q6',
    questionText: 'What is the purpose of useEffect hook in React?',
    type: 'Short Answer',
    difficulty: 'Medium',
    category: 'React',
    tags: ['hooks', 'lifecycle', 'side-effects'],
    points: 2
  },
  {
    _id: 'q7',
    questionText: 'Which of the following is the correct way to handle state in functional components?',
    type: 'MCQ',
    difficulty: 'Easy',
    category: 'React',
    tags: ['state', 'hooks', 'functional-components'],
    options: [
      'useState hook',
      'this.setState',
      'setState function',
      'state property'
    ],
    correctAnswer: 0,
    points: 1
  },

  // Data Structures Questions
  {
    _id: 'q8',
    questionText: 'Implement a binary search algorithm.',
    type: 'Coding',
    difficulty: 'Medium',
    category: 'Data Structures',
    tags: ['algorithms', 'searching', 'binary-search'],
    points: 3
  },
  {
    _id: 'q9',
    questionText: 'What is the time complexity of inserting an element at the beginning of an array?',
    type: 'MCQ',
    difficulty: 'Easy',
    category: 'Data Structures',
    tags: ['arrays', 'time-complexity', 'insertion'],
    options: [
      'O(n)',
      'O(1)',
      'O(log n)',
      'O(n²)'
    ],
    correctAnswer: 0,
    points: 1
  },

  // Backend Development Questions
  {
    _id: 'q10',
    questionText: 'Explain the difference between SQL and NoSQL databases.',
    type: 'Short Answer',
    difficulty: 'Medium',
    category: 'Backend Development',
    tags: ['databases', 'sql', 'nosql'],
    points: 2
  },
  {
    _id: 'q11',
    questionText: 'What is RESTful API design?',
    type: 'Short Answer',
    difficulty: 'Medium',
    category: 'Backend Development',
    tags: ['api', 'rest', 'web-services'],
    points: 2
  },

  // Frontend Development Questions
  {
    _id: 'q12',
    questionText: 'What is the CSS Box Model?',
    type: 'Short Answer',
    difficulty: 'Easy',
    category: 'Frontend Development',
    tags: ['css', 'layout', 'box-model'],
    points: 1
  },
  {
    _id: 'q13',
    questionText: 'Which CSS property is used to make text bold?',
    type: 'MCQ',
    difficulty: 'Easy',
    category: 'Frontend Development',
    tags: ['css', 'typography'],
    options: [
      'font-weight',
      'text-weight',
      'font-style',
      'text-style'
    ],
    correctAnswer: 0,
    points: 1
  },

  // UI/UX Design Questions
  {
    _id: 'q14',
    questionText: 'What are the principles of good UI design?',
    type: 'Short Answer',
    difficulty: 'Medium',
    category: 'UI/UX Design',
    tags: ['design-principles', 'user-interface'],
    points: 2
  },
  {
    _id: 'q15',
    questionText: 'What is the difference between UX and UI design?',
    type: 'Short Answer',
    difficulty: 'Easy',
    category: 'UI/UX Design',
    tags: ['ux', 'ui', 'design'],
    points: 1
  },

  // DevOps Questions
  {
    _id: 'q16',
    questionText: 'What is Docker and how does it work?',
    type: 'Short Answer',
    difficulty: 'Medium',
    category: 'DevOps',
    tags: ['docker', 'containerization', 'deployment'],
    points: 2
  },
  {
    _id: 'q17',
    questionText: 'Which of the following is a container orchestration platform?',
    type: 'MCQ',
    difficulty: 'Medium',
    category: 'DevOps',
    tags: ['kubernetes', 'orchestration', 'containers'],
    options: [
      'Kubernetes',
      'Docker',
      'Jenkins',
      'Git'
    ],
    correctAnswer: 0,
    points: 2
  },

  // Database Questions
  {
    _id: 'q18',
    questionText: 'Write a SQL query to find the second highest salary from an Employee table.',
    type: 'Coding',
    difficulty: 'Medium',
    category: 'Database',
    tags: ['sql', 'queries', 'subqueries'],
    points: 3
  },
  {
    _id: 'q19',
    questionText: 'What is database normalization?',
    type: 'Short Answer',
    difficulty: 'Medium',
    category: 'Database',
    tags: ['normalization', 'database-design'],
    points: 2
  },

  // Node.js Questions
  {
    _id: 'q20',
    questionText: 'What is the event loop in Node.js?',
    type: 'Short Answer',
    difficulty: 'Hard',
    category: 'Node.js',
    tags: ['event-loop', 'asynchronous', 'javascript'],
    points: 3
  }
];

export const mockCandidates = [
  {
    _id: 'c1',
    name: 'Ganesh Shit',
    email: '<EMAIL>',
    skills: ['JavaScript', 'React', 'Node.js'],
    experience: '3',
    location: 'New York',
    status: 'available'
  },
  {
    _id: 'c2',
    name: 'Ganesh',
    email: '<EMAIL>',
    skills: ['Python', 'Django', 'PostgreSQL'],
    experience: '5',
    location: 'San Francisco',
    status: 'available'
  },
  {
    _id: 'c3',
    name: 'Mike Johnson',
    email: '<EMAIL>',
    skills: ['Java', 'Spring Boot', 'MySQL'],
    experience: '4',
    location: 'Chicago',
    status: 'interviewing'
  },
  {
    _id: 'c4',
    name: 'Sarah Wilson',
    email: '<EMAIL>',
    skills: ['React', 'TypeScript', 'GraphQL'],
    experience: '2',
    location: 'Austin',
    status: 'available'
  },
  {
    _id: 'c5',
    name: 'David Brown',
    email: '<EMAIL>',
    skills: ['Python', 'Machine Learning', 'TensorFlow'],
    experience: '6',
    location: 'Seattle',
    status: 'available'
  },
  {
    _id: 'c6',
    name: 'Emily Davis',
    email: '<EMAIL>',
    skills: ['UI/UX Design', 'Figma', 'Adobe XD'],
    experience: '3',
    location: 'Los Angeles',
    status: 'available'
  },
  {
    _id: 'c7',
    name: 'Alex Chen',
    email: '<EMAIL>',
    skills: ['DevOps', 'AWS', 'Docker', 'Kubernetes'],
    experience: '5',
    location: 'Boston',
    status: 'available'
  },
  {
    _id: 'c8',
    name: 'Lisa Garcia',
    email: '<EMAIL>',
    skills: ['Full Stack', 'MEAN Stack', 'MongoDB'],
    experience: '4',
    location: 'Miami',
    status: 'available'
  }
];

export const mockQuestionBundles = [
  {
    _id: 'b1',
    name: 'JavaScript Fundamentals',
    description: 'Basic JavaScript concepts including variables, functions, and closures',
    questions: [
      { questionId: 'q1', points: 2 },
      { questionId: 'q2', points: 3 },
      { questionId: 'q3', points: 2 }
    ],
    totalQuestions: 3,
    totalPoints: 7,
    categories: ['JavaScript'],
    createdAt: new Date('2024-01-15')
  },
  {
    _id: 'b2',
    name: 'React Development',
    description: 'React hooks, components, and state management',
    questions: [
      { questionId: 'q6', points: 2 },
      { questionId: 'q7', points: 1 }
    ],
    totalQuestions: 2,
    totalPoints: 3,
    categories: ['React', 'Frontend Development'],
    createdAt: new Date('2024-01-20')
  },
  {
    _id: 'b3',
    name: 'Data Structures & Algorithms',
    description: 'Core computer science concepts',
    questions: [
      { questionId: 'q8', points: 3 },
      { questionId: 'q9', points: 1 }
    ],
    totalQuestions: 2,
    totalPoints: 4,
    categories: ['Data Structures', 'Algorithms'],
    createdAt: new Date('2024-01-25')
  },
  {
    _id: 'b4',
    name: 'Full Stack Development',
    description: 'Comprehensive full stack development questions',
    questions: [
      { questionId: 'q10', points: 2 },
      { questionId: 'q11', points: 2 },
      { questionId: 'q12', points: 1 }
    ],
    totalQuestions: 3,
    totalPoints: 5,
    categories: ['Backend Development', 'Frontend Development'],
    createdAt: new Date('2024-02-01')
  }
];


