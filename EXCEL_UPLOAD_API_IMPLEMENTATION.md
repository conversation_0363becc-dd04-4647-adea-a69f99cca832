# Excel Upload API Implementation

## Overview
This document describes the implementation of the Excel upload functionality for the question management system. The API allows companies to upload questions in bulk using Excel files.

## API Endpoint
```
POST /questions/upload-excel
```

## Frontend Implementation

### 1. Constants Update
Added the upload endpoint to `src/lib/constants.js`:
```javascript
export const QUESTION_ENDPOINTS = {
    // ... existing endpoints
    UPLOAD_EXCEL: `${API_BASE_URL}/questions/upload-excel`,
};
```

### 2. Store Function
Added `uploadQuestionsFromExcel` function to `src/store/companyStore.js`:
```javascript
uploadQuestionsFromExcel: async (file) => {
    try {
        const formData = new FormData();
        formData.append('excel', file);

        const res = await axiosInstance.post(QUESTION_ENDPOINTS.UPLOAD_EXCEL, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });

        return {
            success: true,
            data: res.data,
            message: res.data.message,
            summary: res.data.summary
        };
    } catch (err) {
        const errorData = err?.response?.data;
        return {
            success: false,
            error: errorData?.error || 'Excel upload failed',
            details: errorData?.details,
            validationErrors: errorData?.validationErrors,
            expectedFormat: errorData?.expectedFormat
        };
    }
}
```

### 3. Hook Update
Updated `src/Components/company/hooks/useQuestionManager.js` to use the API instead of client-side parsing:
- Replaced client-side XLSX parsing with API call
- Added proper error handling for validation errors
- Maintained backward compatibility with existing interface

### 4. Component Enhancement
Enhanced `src/Components/company/components/QuestionUpload.jsx`:
- Updated Excel format requirements display
- Added detailed upload results display
- Enhanced error handling with validation error details
- Improved user feedback with summary information

## Excel File Format

### Required Columns
- **Question**: Question text
- **Type**: MCQ, Multiple-Select, Short-Answer, Code
- **Category**: Frontend, Backend, Full Stack, Data Science, DevOps, Mobile, UI/UX, QA, Aptitude, Logical, Other
- **Difficulty**: Easy, Medium, Hard

### Optional Columns
- **Option1-Option4**: Answer options (for MCQ/Multiple-Select)
- **Correct**: Correct answer
- **Explanation**: Answer explanation
- **Points**: Question points (default: 1)

### Supported Categories
- Frontend
- Backend
- Full Stack
- Data Science
- DevOps
- Mobile
- UI/UX
- QA
- Aptitude
- Logical
- Other

### Supported Question Types
- **MCQ**: Multiple Choice Question
- **Multiple-Select**: Multiple selection question
- **Short-Answer**: Short text answer
- **Code**: Code-based question

## API Response Format

### Success Response
```json
{
    "success": true,
    "message": "Successfully processed Excel file",
    "summary": {
        "totalRowsProcessed": 10,
        "questionsInserted": 8,
        "duplicatesSkipped": 1,
        "validationErrors": 1
    },
    "questions": [...]
}
```

### Error Response
```json
{
    "success": false,
    "error": "Validation failed for some questions",
    "validationErrors": [
        {
            "row": 2,
            "errors": ["Question text is required", "Invalid question type"]
        }
    ],
    "expectedFormat": {
        "requiredColumns": ["Question", "Type", "Category", "Difficulty"],
        "supportedTypes": ["MCQ", "Multiple-Select", "Short-Answer", "Code"],
        "supportedCategories": ["Frontend", "Backend", "Full Stack", ...]
    }
}
```

## Features

### 1. File Validation
- File type validation (.xlsx, .xls)
- File size validation (max 10MB)
- Required column validation

### 2. Data Processing
- Batch processing for large files
- Duplicate detection and skipping
- Comprehensive validation per question
- Detailed error reporting

### 3. User Experience
- Drag and drop file upload
- Real-time upload progress
- Detailed success/error feedback
- Format requirements display
- Validation error details

### 4. Error Handling
- Row-level validation errors
- Format validation
- Duplicate handling
- Graceful error recovery

## Testing

### Sample Excel File
A sample Excel file (`sample_questions.xlsx`) has been created with the correct format for testing purposes.

### Test Cases
- Valid Excel file upload
- Invalid file format
- Missing required columns
- Validation errors
- Duplicate questions
- Large file handling

## Usage Example

1. Navigate to the Question Upload section
2. Drag and drop or select an Excel file
3. Review the format requirements
4. Upload the file
5. Review the upload results and any validation errors
6. Address any issues and re-upload if necessary

## Backend Requirements

The backend should implement the `/questions/upload-excel` endpoint as described in the provided controller code, including:
- File upload handling with multer
- Excel parsing with XLSX
- Question validation
- Duplicate detection
- Batch insertion
- Comprehensive error reporting
