import React, { useState } from 'react';
import {
    BriefcaseIcon,
    MapPinIcon,
    CalendarIcon,
    EyeIcon,
    PencilIcon,
    UserGroupIcon,
    ClockIcon,
    CheckCircleIcon,
    XCircleIcon,
    EllipsisVerticalIcon,
    ChartBarIcon,
    DocumentTextIcon,
    CurrencyDollarIcon
} from '@heroicons/react/24/outline';
import {
    CheckCircleIcon as CheckCircleSolid,
    XCircleIcon as XCircleSolid,
    ClockIcon as ClockSolid,
    FireIcon
} from '@heroicons/react/24/solid';
import { motion, AnimatePresence } from 'framer-motion';

const JobTable = ({ jobs, onEdit, onStatusUpdate, onViewApplications, onViewDetails }) => {
    const [sortField, setSortField] = useState('createdAt');
    const [sortDirection, setSortDirection] = useState('desc');
    const [activeDropdown, setActiveDropdown] = useState(null);

    const formatSalary = (salary) => {
        if (!salary || typeof salary !== 'object') return 'Not specified';

        const { min, max, currency } = salary;
        const symbolMap = {
            INR: '₹',
            USD: '$',
            EUR: '€',
            GBP: '£'
        };

        const symbol = symbolMap[currency] || currency;
        const format = (val) => {
            if (val >= 100000) {
                return `${(val / 100000).toFixed(1)}L`;
            }
            return val.toLocaleString('en-IN', { maximumFractionDigits: 0 });
        };

        return `${symbol}${format(min)} - ${symbol}${format(max)}`;
    };

    const formatDate = (date) => {
        const now = new Date();
        const jobDate = new Date(date);
        const diffTime = Math.abs(now - jobDate);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) return 'Today';
        if (diffDays === 2) return 'Yesterday';
        if (diffDays <= 7) return `${diffDays} days ago`;
        return jobDate.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: jobDate.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
        });
    };

    const formatDeadline = (deadline) => {
        const now = new Date();
        const deadlineDate = new Date(deadline);
        const diffTime = deadlineDate - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays < 0) return { text: 'Expired', color: 'text-red-600', urgent: true };
        if (diffDays === 0) return { text: 'Today', color: 'text-red-600', urgent: true };
        if (diffDays === 1) return { text: '1 day left', color: 'text-orange-600', urgent: true };
        if (diffDays <= 7) return { text: `${diffDays} days left`, color: 'text-orange-600', urgent: false };
        return { text: `${diffDays} days left`, color: 'text-green-600', urgent: false };
    };

    const getJobStatusInfo = (job) => {
        const { isActive, applications = 0, maxApplications = 0, applicationDeadline } = job;
        const deadline = applicationDeadline ? formatDeadline(applicationDeadline) : null;
        const isExpired = deadline?.text === 'Expired';
        const applicationsFull = applications >= maxApplications;

        if (!isActive) {
            return {
                status: 'Inactive',
                color: 'bg-gray-100 text-gray-700',
                icon: XCircleSolid,
                iconColor: 'text-gray-500'
            };
        }

        if (isExpired || applicationsFull) {
            return {
                status: 'Closed',
                color: 'bg-red-100 text-red-700',
                icon: XCircleSolid,
                iconColor: 'text-red-500'
            };
        }

        if (deadline?.urgent) {
            return {
                status: 'Urgent',
                color: 'bg-orange-100 text-orange-700',
                icon: ClockSolid,
                iconColor: 'text-orange-500'
            };
        }

        return {
            status: 'Active',
            color: 'bg-green-100 text-green-700',
            icon: CheckCircleSolid,
            iconColor: 'text-green-500'
        };
    };

    const handleSort = (field) => {
        if (sortField === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
    };

    const sortedJobs = [...jobs].sort((a, b) => {
        let aValue = a[sortField];
        let bValue = b[sortField];

        // Handle special cases
        if (sortField === 'createdAt' || sortField === 'applicationDeadline') {
            aValue = new Date(aValue);
            bValue = new Date(bValue);
        }

        if (sortField === 'salary') {
            aValue = a.salary?.min || 0;
            bValue = b.salary?.min || 0;
        }

        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
    });

    const handleStatusUpdate = async (jobId, isActive) => {
        try {
            await onStatusUpdate(jobId, isActive);
            setActiveDropdown(null);
        } catch (error) {
            console.error('Status update failed:', error);
        }
    };

    const SortButton = ({ field, children }) => (
        <button
            onClick={() => handleSort(field)}
            className="flex items-center gap-1 text-left font-medium text-gray-900 hover:text-blue-600 transition-colors"
        >
            {children}
            {sortField === field && (
                <span className="text-blue-600">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                </span>
            )}
        </button>
    );

    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                        <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <SortButton field="title">Job Title</SortButton>
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <SortButton field="location">Location</SortButton>
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <SortButton field="jobType">Type</SortButton>
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <SortButton field="salary">Salary</SortButton>
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <SortButton field="applications">Applications</SortButton>
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <SortButton field="createdAt">Created</SortButton>
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Deadline
                            </th>
                            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {sortedJobs.map((job) => {
                            const statusInfo = getJobStatusInfo(job);
                            const StatusIcon = statusInfo.icon;
                            const deadline = job.applicationDeadline ? formatDeadline(job.applicationDeadline) : null;
                            const applicationProgress = job.maxApplications ? (job.applications / job.maxApplications) * 100 : 0;

                            return (
                                <motion.tr
                                    key={job._id}
                                    className="hover:bg-gray-50 transition-colors"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ duration: 0.2 }}
                                >
                                    {/* Job Title */}
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex items-center">
                                            <div className="flex-shrink-0 h-10 w-10">
                                                <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                                                    <BriefcaseIcon className="h-5 w-5 text-blue-600" />
                                                </div>
                                            </div>
                                            <div className="ml-4">
                                                <div className="flex items-center gap-2">
                                                    <div className="text-sm font-medium text-gray-900 max-w-[200px] truncate">
                                                        {job.title}
                                                    </div>
                                                    {job.hasTest && (
                                                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-700">
                                                            <DocumentTextIcon className="w-3 h-3 mr-1" />
                                                            Test
                                                        </span>
                                                    )}
                                                </div>
                                                <div className="text-sm text-gray-500">{job.companyName}</div>
                                            </div>
                                        </div>
                                    </td>

                                    {/* Location */}
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex items-center text-sm text-gray-900">
                                            <MapPinIcon className="h-4 w-4 text-blue-500 mr-1" />
                                            {job.location}
                                        </div>
                                        {job.workMode && (
                                            <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-1 ${
                                                job.workMode === 'Remote' ? 'bg-blue-100 text-blue-700' :
                                                job.workMode === 'Hybrid' ? 'bg-purple-100 text-purple-700' :
                                                'bg-gray-100 text-gray-700'
                                            }`}>
                                                {job.workMode}
                                            </div>
                                        )}
                                    </td>

                                    {/* Type */}
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex items-center text-sm text-gray-900">
                                            <BriefcaseIcon className="h-4 w-4 text-green-500 mr-1" />
                                            {job.jobType}
                                        </div>
                                        {job.experienceLevel && (
                                            <div className="text-xs text-gray-500 mt-1">{job.experienceLevel}</div>
                                        )}
                                    </td>

                                    {/* Salary */}
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex items-center text-sm text-gray-900">
                                            <CurrencyDollarIcon className="h-4 w-4 text-green-500 mr-1" />
                                            {formatSalary(job.salary)}
                                        </div>
                                    </td>

                                    {/* Applications */}
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex items-center">
                                            <UserGroupIcon className="h-4 w-4 text-purple-500 mr-2" />
                                            <div className="flex-1">
                                                <div className="text-sm font-medium text-gray-900">
                                                    {job.applications || 0}{job.maxApplications ? `/${job.maxApplications}` : ''}
                                                </div>
                                                {job.maxApplications && (
                                                    <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                                        <div
                                                            className={`h-1.5 rounded-full transition-all duration-500 ${
                                                                applicationProgress >= 90 ? 'bg-red-500' :
                                                                applicationProgress >= 70 ? 'bg-orange-500' :
                                                                'bg-green-500'
                                                            }`}
                                                            style={{ width: `${Math.min(applicationProgress, 100)}%` }}
                                                        />
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </td>

                                    {/* Status */}
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.color}`}>
                                            <StatusIcon className={`w-3 h-3 mr-1 ${statusInfo.iconColor}`} />
                                            {statusInfo.status}
                                        </span>
                                    </td>

                                    {/* Created */}
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex items-center text-sm text-gray-900">
                                            <CalendarIcon className="h-4 w-4 text-purple-500 mr-1" />
                                            {formatDate(job.createdAt)}
                                        </div>
                                    </td>

                                    {/* Deadline */}
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        {deadline && (
                                            <div className="flex items-center">
                                                <ClockIcon className={`h-4 w-4 mr-1 ${deadline.color}`} />
                                                <span className={`text-sm font-medium ${deadline.color}`}>
                                                    {deadline.text}
                                                </span>
                                                {deadline.urgent && (
                                                    <FireIcon className="w-4 h-4 text-orange-500 animate-pulse ml-1" />
                                                )}
                                            </div>
                                        )}
                                    </td>

                                    {/* Actions */}
                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div className="flex items-center justify-end gap-2">
                                            <button
                                                onClick={() => onViewApplications && onViewApplications(job._id)}
                                                className="text-blue-600 hover:text-blue-900 transition-colors"
                                                title="View Applications"
                                            >
                                                <EyeIcon className="h-4 w-4" />
                                            </button>
                                            <button
                                                onClick={() => onEdit(job)}
                                                className="text-gray-600 hover:text-gray-900 transition-colors"
                                                title="Edit Job"
                                            >
                                                <PencilIcon className="h-4 w-4" />
                                            </button>
                                            
                                            {/* Dropdown Menu */}
                                            <div className="relative">
                                                <button
                                                    onClick={() => setActiveDropdown(activeDropdown === job._id ? null : job._id)}
                                                    className="text-gray-400 hover:text-gray-600 transition-colors"
                                                >
                                                    <EllipsisVerticalIcon className="h-4 w-4" />
                                                </button>

                                                <AnimatePresence>
                                                    {activeDropdown === job._id && (
                                                        <motion.div
                                                            className="absolute right-0 top-full mt-1 w-40 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-20"
                                                            initial={{ opacity: 0, scale: 0.95, y: -10 }}
                                                            animate={{ opacity: 1, scale: 1, y: 0 }}
                                                            exit={{ opacity: 0, scale: 0.95, y: -10 }}
                                                            transition={{ duration: 0.1 }}
                                                        >
                                                            <button
                                                                onClick={() => handleStatusUpdate(job._id, !job.isActive)}
                                                                className={`w-full flex items-center gap-2 px-3 py-2 text-sm transition-colors ${
                                                                    job.isActive
                                                                        ? 'text-red-700 hover:bg-red-50'
                                                                        : 'text-green-700 hover:bg-green-50'
                                                                }`}
                                                            >
                                                                {job.isActive ? (
                                                                    <>
                                                                        <XCircleIcon className="w-4 h-4" />
                                                                        Deactivate
                                                                    </>
                                                                ) : (
                                                                    <>
                                                                        <CheckCircleIcon className="w-4 h-4" />
                                                                        Activate
                                                                    </>
                                                                )}
                                                            </button>
                                                        </motion.div>
                                                    )}
                                                </AnimatePresence>
                                            </div>
                                        </div>
                                    </td>
                                </motion.tr>
                            );
                        })}
                    </tbody>
                </table>
            </div>

            {/* Overlay for click outside dropdown */}
            {activeDropdown && (
                <div
                    className="fixed inset-0 z-10"
                    onClick={() => setActiveDropdown(null)}
                />
            )}
        </div>
    );
};

export default JobTable;
