import { useRef } from "react";
import { FaQuoteLeft } from "react-icons/fa";
import {
  motion,
  useInView,
  useAnimation,
  AnimatePresence,
} from "framer-motion";
import user1 from "../assets/user1.png";
import user2 from "../assets/user2.png";
import user3 from "../assets/user3.png";

// Testimonials data
const testimonials = [
  {
    name: "<PERSON><PERSON>",
    role: "Software Engineer",
    quote:
      "<PERSON><PERSON><PERSON>’s mock interviews helped me crack my first job at a top MNC!",
    avatar: user1,
  },
  {
    name: "<PERSON><PERSON>",
    role: "HR Manager",
    quote: "I learned how to face and conduct interviews. Game-changing!",
    avatar: user2,
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "Data Analyst",
    quote:
      "Domain-specific aptitude tests made me confident for technical rounds.",
    avatar: user3,
  },
];

// Blue/yellow accent gradients, same as 'HowItWorks' car and blue dot
const gradientBorders = [
  "from-[#3069f1] to-[#FCA311]",
  "from-[#FCA311] to-[#3069f1]",
  "from-[#3069f1] to-[#61DAFB]",
];

// Animation variants for the cards fan-out effect in a 3-column grid
const cardVariants = [
  {
    initial: { x: 0, y: 0, scale: 0.82, opacity: 0, filter: "blur(7px)" },
    animate: { x: -120, y: 0, scale: 1, opacity: 1, filter: "blur(0px)" },
    exit: {
      x: 60,
      opacity: 0,
      scale: 0.85,
      filter: "blur(7px)",
      transition: { duration: 0.35, ease: "easeInOut" },
    },
  },
  {
    initial: { x: 0, y: 0, scale: 0.82, opacity: 0, filter: "blur(7px)" },
    animate: { x: 0, y: -30, scale: 1, opacity: 1, filter: "blur(0px)" },
    exit: {
      x: 0,
      y: 40,
      opacity: 0,
      scale: 0.85,
      filter: "blur(7px)",
      transition: { duration: 0.35, ease: "easeInOut" },
    },
  },
  {
    initial: { x: 0, y: 0, scale: 0.82, opacity: 0, filter: "blur(7px)" },
    animate: { x: 120, y: 0, scale: 1, opacity: 1, filter: "blur(0px)" },
    exit: {
      x: -60,
      opacity: 0,
      scale: 0.85,
      filter: "blur(7px)",
      transition: { duration: 0.35, ease: "easeInOut" },
    },
  },
];

// Icon animation variants
const iconVariants = {
  hidden: { scale: 0.7, opacity: 0, rotate: -30 },
  visible: {
    scale: 1,
    opacity: 1,
    rotate: 0,
    transition: {
      type: "spring",
      stiffness: 320,
      damping: 16,
      duration: 0.55,
    },
  },
};

const quoteVariants = {
  hidden: { scale: 0.7, opacity: 0, rotate: 30 },
  visible: {
    scale: 1,
    opacity: 1,
    rotate: 0,
    transition: {
      type: "spring",
      stiffness: 340,
      damping: 12,
      duration: 0.5,
      delay: 0.12,
    },
  },
};

const Testimonials = ({ dark = true }) => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { amount: 0.4, once: true });
  const controls = useAnimation();

  // Animate when in view
  if (isInView) {
    controls.start((i) => ({
      ...cardVariants[i].animate,
      transition: {
        type: "spring",
        bounce: 0.37,
        stiffness: 90,
        damping: 18,
        delay: 0.2 + i * 0.2,
      },
    }));
  }

  // Section colors
  const bgSection = dark
    ? "bg-gradient-to-b from-black via-neutral-900 to-neutral-950"
    : "bg-white";
  const textSection = dark ? "text-white" : "text-black";
  const headingColor = dark ? "#fff" : "#23263a";

  return (
    <section
      ref={sectionRef}
      className={`w-full min-h-screen flex flex-col items-center justify-center relative py-20 px-0 ${bgSection} ${textSection} overflow-hidden`}
      id="Testimonials"
    >
      <div className="pointer-events-none absolute top-0 left-0 w-full h-full z-0 mr-5">
        <div
          className={`absolute -top-20 -left-20 w-80 h-80 rounded-full ${
            dark ? "bg-[#3069f1]/25" : "bg-[#3069f1]/15"
          } blur-3xl opacity-70 animate-pulse`}
        />
        <div
          className={`absolute bottom-0 right-0 w-64 h-64 rounded-full ${
            dark ? "bg-[#FCA311]/20" : "bg-[#FCA311]/10"
          } blur-2xl opacity-60`}
        />
      </div>

      <div className="relative z-10 w-full flex flex-col items-center justify-center">
        <h3
          className="text-4xl md:text-5xl font-extrabold mb-16 tracking-tight drop-shadow-lg w-full text-center p-4"
          style={{
            background: dark
              ? "linear-gradient(90deg, #fff 0%, #fff 100%)"
              : "linear-gradient(90deg, #23263a 0%, #23263a 100%)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
            backgroundClip: "text",
            color: headingColor,
            textShadow: dark
              ? "0 2px 8px rgba(0,0,0,0.12)"
              : "0 2px 8px rgba(35,38,58,0.06)",
          }}
        >
          What Our Users Say
        </h3>
        <div className="relative flex flex-col items-center justify-center w-full ">
          <AnimatePresence>
            <div className="grid gap-10 md:grid-cols-2 lg:grid-cols-3 w-full max-w-7xl mx-auto justify-items-center items-center">
              {testimonials.map((t, i) => (
                <motion.div
                  key={i}
                  className={`
                    relative group flex flex-col items-center text-center
                    p-7 sm:p-8 rounded-[2rem] shadow-2xl min-h-[340px] w-full max-w-md
                    border-2 border-transparent ${
                      dark ? "bg-neutral-900" : "bg-white"
                    } bg-clip-padding
                    before:content-[''] before:absolute before:inset-0 before:z-[-1]
                    before:rounded-[2.05rem]
                    before:bg-gradient-to-br before:opacity-80
                    before:${gradientBorders[i % gradientBorders.length]}
                    before:transition-all before:duration-300
                    group-hover:before:scale-105
                    transition-all duration-300
                  `}
                  style={{
                    boxShadow: dark
                      ? "0 8px 36px 0 #23263a50, 0 2px 8px 0 #3069f135"
                      : "0 8px 36px 0 #23263a20, 0 2px 8px 0 #3069f125",
                    overflow: "visible",
                    margin: "0 auto",
                  }}
                  custom={i}
                  initial={cardVariants[i].initial}
                  animate={controls}
                  exit={cardVariants[i].exit}
                >
                  {/* Message bubble tail - left */}
                  <svg
                    width="40"
                    height="24"
                    viewBox="0 0 40 24"
                    className="absolute -left-4 top-12 hidden md:block"
                    style={{
                      filter: dark
                        ? "drop-shadow(0 2px 8px #2223a033)"
                        : "drop-shadow(0 2px 8px #2223a019)",
                    }}
                  >
                    <path
                      d="M40 0C40 13.255 16.09 17.206 2.804 23.448C1.192 24.216 0 23.367 0 21.6V0H40Z"
                      fill={dark ? "#18181b" : "#fff"}
                    />
                  </svg>
                  {/* User Avatar and Icon */}
                  <div className="flex items-center justify-center space-x-3 mb-5">
                    <motion.img
                      src={t.avatar}
                      alt={t.name}
                      className={`w-14 h-14 rounded-full border-4 border-[#3069f1] shadow-lg bg-white object-cover`}
                      style={{
                        boxShadow: "0 2px 10px #3069f180",
                        background: "white",
                      }}
                      initial="hidden"
                      animate={isInView ? "visible" : "hidden"}
                      variants={iconVariants}
                      transition={{ delay: 0.22 + i * 0.16 }}
                    />
                    <motion.span
                      initial="hidden"
                      animate={isInView ? "visible" : "hidden"}
                      variants={quoteVariants}
                      transition={{ delay: 0.32 + i * 0.16 }}
                    >
                      <FaQuoteLeft className="text-[#3069f1] text-3xl drop-shadow" />
                    </motion.span>
                  </div>
                  {/* Quote */}
                  <p
                    className={`text-lg sm:text-xl font-medium leading-relaxed mb-7 italic ${
                      dark ? "text-neutral-200" : "text-[#23263a]"
                    }`}
                  >
                    “{t.quote}”
                  </p>
                  {/* Name & Role */}
                  <div className="mt-auto flex flex-col items-center space-y-1">
                    <p className="text-[#FCA311] font-bold text-lg">{t.name}</p>
                    <p className="text-sm text-[#3069f1] font-medium">
                      {t.role}
                    </p>
                  </div>
                  {/* Message bubble tail - mobile (bottom) */}
                  <svg
                    width="40"
                    height="20"
                    viewBox="0 0 40 20"
                    className="absolute left-1/2 -translate-x-1/2 -bottom-4 block md:hidden"
                    style={{
                      filter: dark
                        ? "drop-shadow(0 2px 8px #2223a033)"
                        : "drop-shadow(0 2px 8px #2223a019)",
                    }}
                  >
                    <path
                      d="M0 0C0 11.05 25.003 14.358 37.333 19.06C38.815 19.65 40 18.879 40 17.24V0H0Z"
                      fill={dark ? "#18181b" : "#fff"}
                    />
                  </svg>
                </motion.div>
              ))}
            </div>
          </AnimatePresence>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
