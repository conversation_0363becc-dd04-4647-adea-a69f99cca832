# Candidate Details Implementation

## Overview
This implementation adds comprehensive candidate viewing functionality to the job card system, allowing companies to view detailed candidate information, resumes, and test results for job applications.

## Features Implemented

### 1. API Endpoints Added
**File:** `src/lib/constants.js`

New endpoints added to `COMPANY_ENDPOINTS`:
- `JOB_APPLICATIONS_WITH_RESUMES`: Get applications with resume data for a specific job
- `JOB_CANDIDATES`: Get candidates for a specific job
- `CANDIDATE_DETAILS`: Get detailed candidate information
- `APPLICATIONS_WITH_RESUMES`: Search across all applications with resumes
- `CANDIDATES_ANALYTICS`: Get analytics for candidates of a specific job

### 2. Store Methods Added
**File:** `src/store/companyStore.js`

New methods added:
- `getJobApplicationsWithResumes(jobId, params)`: Fetch applications with resume data
- `getAllApplicationsWithResumes(params)`: Search across all applications
- `getCandidateDetails(jobId, candidateId)`: Get detailed candidate information
- `getCandidatesAnalytics(jobId)`: Get candidate analytics for a job

### 3. Components Created

#### JobCandidatesModal
**File:** `src/Components/company/components/JobCandidatesModal.jsx`

Features:
- Lists all candidates who applied for a specific job
- Advanced search and filtering capabilities
- Filter by status, experience level, skills, and location
- Displays candidate cards with key information
- Integration with candidate details modal

#### CandidateDetailsModal
**File:** `src/Components/company/components/CandidateDetailsModal.jsx`

Features:
- Three-tab interface: Overview, Resume, Test Results
- **Overview Tab**: Contact info, application status, social profiles, skills, experience, education
- **Resume Tab**: Full resume display using ResumeTemplate
- **Test Results Tab**: Test performance and status

#### ResumeTemplate
**File:** `src/Components/company/components/ResumeTemplate.jsx`

Features:
- Professional resume layout with sections for:
  - Header with profile picture and contact info
  - Summary/Objective
  - Experience with timeline
  - Education
  - Skills with proficiency levels
  - Projects with technologies
  - Certifications
  - Languages
  - Awards
  - Publications
  - Social profiles
- Responsive design with color-coded sections
- Handles missing data gracefully

### 4. Integration with Existing Components

#### CreateJob Component
**File:** `src/Components/company/CreateJob.jsx`

Changes made:
- Added import for `JobCandidatesModal`
- Added state for modal management
- Added `handleViewApplications` function
- Added `handleCloseCandidatesModal` function
- Updated `onViewApplications` prop to use new handler
- Added `JobCandidatesModal` component to JSX

#### JobCard Component
**File:** `src/Components/company/components/JobCard.jsx`

The existing "View" button now triggers the candidate viewing functionality through the `onViewApplications` prop.

## API Integration

### Expected API Response Format

Based on the provided sample data, the API should return:

```json
{
    "success": true,
    "data": [
        {
            "job": {
                "id": "687f1ec4d248eae1d5cf3950",
                "title": "Backend developer",
                "category": "Backend",
                "location": "San Francisco, CA",
                "experienceLevel": "Mid",
                "hasTest": true
            },
            "application": {
                "appliedAt": "2025-07-23T05:15:25.429Z",
                "status": "test_pending",
                "testScore": null
            },
            "candidate": {
                "id": "687b918c29bed4831477d6e9",
                "name": "Adarsh",
                "email": "<EMAIL>",
                "totalExperience": 0
            },
            "resume": {
                "_id": "687b91d629bed4831477d6f1",
                "Title": "Adarsh",
                "Email": "<EMAIL>",
                "Template": "default",
                "Headline": "Full stack developer",
                "summery": "Hello My",
                "Phone": "9749038945",
                "Location": "Bengaluru",
                "Website": "https://ganesh.com",
                "ProfilePic": "https://res.cloudinary.com/...",
                "Skills": [...],
                "Experience": [...],
                "Education": [...],
                "Projects": [...],
                "Certifications": [...],
                "Languages": [...],
                "Awards": [...],
                "Publications": [...],
                "Volunteering": [...],
                "References": [...],
                "Profiles": [...]
            },
            "testResults": {
                "status": "assigned"
            }
        }
    ]
}
```

## Usage Flow

1. **Company views job cards** in the jobs dashboard
2. **Clicks "View" button** on a job card
3. **JobCandidatesModal opens** showing all candidates for that job
4. **Company can search/filter** candidates by various criteria
5. **Clicks "View Details"** on a candidate card
6. **CandidateDetailsModal opens** with three tabs:
   - Overview: Key candidate information
   - Resume: Full resume display
   - Test Results: Test performance data
7. **Company can navigate** between tabs to view different aspects
8. **Close modals** to return to job dashboard

## Filtering and Search Capabilities

### Search
- Search by candidate name
- Search by email
- Search by skills

### Filters
- Application status (applied, test_pending, test_completed, shortlisted, rejected)
- Experience level (entry, mid, senior)
- Skills (text input)
- Location (text input)

## Responsive Design

All components are built with responsive design principles:
- Mobile-first approach
- Grid layouts that adapt to screen size
- Touch-friendly buttons and interactions
- Optimized modal sizes for different devices

## Error Handling

- Graceful handling of missing data
- Loading states for API calls
- Error messages for failed requests
- Fallback displays for empty states

## Performance Considerations

- Lazy loading of candidate details
- Efficient filtering and search
- Optimized re-renders with proper state management
- Image optimization with error handling

## Next Steps

To complete the implementation:

1. **Backend API Development**: Implement the API endpoints as defined in constants
2. **Testing**: Add unit tests for components and integration tests for API calls
3. **Analytics Integration**: Add candidate analytics dashboard
4. **Export Functionality**: Add ability to export candidate data
5. **Bulk Actions**: Add bulk candidate management features
6. **Email Integration**: Add email communication features
7. **Interview Scheduling**: Integrate with calendar systems

## Dependencies

The implementation uses existing dependencies:
- React with Hooks
- Framer Motion for animations
- Heroicons for icons
- React Hot Toast for notifications
- Tailwind CSS for styling

No additional dependencies were added to maintain compatibility with the existing codebase.
