import React, { useEffect } from "react";
import { useAuth0 } from "@auth0/auth0-react";
import { DotLottieReact } from "@lottiefiles/dotlottie-react";

export default function PHPLoginBridge() {
  const { isAuthenticated, getIdTokenClaims, isLoading } = useAuth0();

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      getIdTokenClaims().then((claims) => {
        const idToken = claims.__raw;
        const endpoints = [
          "https://appttitude.proyuj.com/auth0-login.php"
          
        ];
        // Send POST to both endpoints
        Promise.all(
          endpoints.map((url) =>
            fetch(url, {
              method: "POST",
              headers: {
                Authorization: "Bearer " + idToken,
                "Content-Type": "application/json",
              },
              credentials: "include",
            }).then((r) => r.json())
          )
        )
          .then((responses) => {
            // Check if all responses are ok
            const allOk = responses.every((resp) => resp.status === "ok");
            if (allOk) {
              window.location.href = "/dashboard";
            } else {
              const firstError = responses.find((r) => r.status !== "ok");
              alert(
                "PHP login failed: " + (firstError?.error || "Unknown error!")
              );
            }
          })
          .catch(() => alert("Network error talking to PHP backend"));
      });
    }
  }, [isAuthenticated, isLoading, getIdTokenClaims]);

  return (
    <DotLottieReact
      src="https://lottie.host/454c3626-2618-4344-b957-5f9c8d674a99/UVood7R6b1.lottie"
      loop
      autoplay
    />
  );
}
