import React from "react";
import {
  FaUserTie,
  FaBrain,
  FaCertificate,
  FaComments,
  FaArrowRight,
  FaStar,
  FaUsers,
  FaClock,
} from "react-icons/fa";

const services = [
  {
    title: "Live Mock Interviews",
    desc: "Experience real-time interviews with industry professionals who provide personalized feedback on your strengths and areas of improvement.",
    icon: <FaUserTie className="text-4xl" />,
    link: "https://interviewpage-inky.vercel.app/",
    color: "from-[#FCA311] to-[#FF7E5F]",
    bgGradient: "from-[#FCA311]/10 to-[#FF7E5F]/5",
    features: [
      "1-on-1 Sessions",
      "Expert Feedback",
      "Personalized Growth Plan",
    ],
    badge: "Most Popular",
  },
  {
    title: "Domain-Specific Aptitude Training",
    desc: "Get tested on technical, logical, and verbal aptitude based on your chosen field (IT, Finance, Marketing, etc.) to ensure you’re interview-ready.",
    icon: <FaBrain className="text-4xl" />,
    link: "https://apttitude.proyuj.com",
    color: "from-[#61DAFB] to-[#4FC3F7]",
    bgGradient: "from-[#61DAFB]/10 to-[#4FC3F7]/5",
    features: [
      "Technical, Logical, Verbal",
      "Field-Specific Modules",
      "Instant Analytics",
    ],
    badge: "Tailored Practice",
  },
  {
    title: "Interviewer Training",
    desc: "Learn how to conduct interviews like a pro—understand question framing, candidate evaluation, and hiring best practices.",
    icon: <FaCertificate className="text-4xl" />,
    link: "https://proyuj.com/certified-interviewer",
    color: "from-[#FF7E5F] to-[#FEB47B]",
    bgGradient: "from-[#FF7E5F]/10 to-[#FEB47B]/5",
    features: [
      "Question Framing",
      "Evaluation Techniques",
      "Hiring Best Practices",
    ],
    badge: "Pro Certified",
  },
  {
    title: "Confidence-Building Workshops",
    desc: "Overcome nervousness, improve communication, and develop a winning interview mindset.",
    icon: <FaComments className="text-4xl" />,
    link: "https://proyuj.com/communication-workshop",
    color: "from-[#FEB47B] to-[#FFD93D]",
    bgGradient: "from-[#FEB47B]/10 to-[#FFD93D]/5",
    features: [
      "Nervousness Overcome",
      "Communication Skills",
      "Winning Mindset",
    ],
    badge: "Confidence Boost",
  },
];

const Services = () => (
  <section
    className="relative py-20 md:py-32 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 overflow-hidden"
    id="Services"
  >
    {/* Decorative Background */}
    <div className="absolute inset-0 pointer-events-none z-0">
      <div className="absolute top-0 left-0 w-full h-full bg-[url('/servicesImage.jpg')] bg-cover bg-center opacity-5" />
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900/95 via-slate-800/90 to-slate-900/95" />
      {/* Animated Blobs */}
      <div className="absolute -top-32 -left-32 w-80 h-80 md:w-96 md:h-96 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF7E5F]/20 blur-3xl opacity-60 animate-pulse" />
      <div className="absolute top-1/2 -right-24 md:-right-32 w-52 h-52 md:w-80 md:h-80 rounded-full bg-gradient-to-l from-[#61DAFB]/15 to-[#4FC3F7]/15 blur-3xl opacity-50 animate-pulse delay-1000" />
      <div className="absolute -bottom-24 left-1/4 w-40 h-40 md:w-72 md:h-72 rounded-full bg-gradient-to-t from-[#FEB47B]/20 to-transparent blur-2xl opacity-40" />
      {/* Floating Sparkles */}
      {[...Array(16)].map((_, i) => (
        <div
          key={i}
          className="absolute w-1.5 h-1.5 bg-[#FCA311]/40 rounded-full animate-pulse"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animationDelay: `${Math.random() * 2.5}s`,
            animationDuration: `${2.5 + Math.random() * 2.5}s`,
          }}
        />
      ))}
    </div>

    <div className="relative z-10 container mx-auto px-4 md:px-8">
      {/* Header */}
      <div className="text-center mb-16 md:mb-20">
        <div className="inline-flex items-center gap-2 bg-gradient-to-r from-[#FCA311]/20 to-[#FF7E5F]/20 backdrop-blur-md border border-[#FCA311]/30 rounded-full px-6 py-2 mb-6 shadow">
          <FaStar className="text-[#FCA311] text-sm" />
          <span className="text-sm font-semibold text-[#FCA311]">
            Premium Services
          </span>
        </div>
        <h3 className="text-4xl sm:text-5xl md:text-7xl font-black bg-gradient-to-r from-white via-[#FCA311] to-white bg-clip-text text-transparent mb-4 md:mb-6 tracking-tight">
          Our Services
        </h3>
        <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
          Comprehensive solutions designed to transform your interview skills
          and accelerate your career growth.
        </p>
      </div>

      {/* Responsive Services Grid */}
      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4 max-w-7xl mx-auto">
        {services.map((service, index) => (
          <div
            key={service.title}
            className={`
              group relative flex flex-col bg-gradient-to-br ${service.bgGradient}
              backdrop-blur-xl border border-white/10 hover:border-white/30 rounded-3xl 
              shadow-2xl hover:shadow-[#FCA311]/20 transition-all duration-500 hover:-translate-y-4
              overflow-hidden min-h-[420px]
              hover:scale-[1.04]
              hover:z-20
              focus-within:ring-2 focus-within:ring-[#FCA311]/60
              `}
            tabIndex={0}
            style={{ animationDelay: `${index * 120}ms` }}
          >
            {/* Badge */}
            <div className="absolute top-4 right-4 z-20">
              <span
                className={`inline-block bg-gradient-to-r ${service.color} text-black text-xs font-bold px-3 py-1 rounded-full shadow-lg`}
              >
                {service.badge}
              </span>
            </div>

            {/* Animated Glow */}
            <div
              className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-15 transition-opacity duration-500 rounded-3xl pointer-events-none`}
            />

            {/* Card Content */}
            <div className="relative z-10 p-6 sm:p-8 flex flex-col h-full">
              {/* Icon */}
              <div
                className={`w-16 h-16 sm:w-20 sm:h-20 rounded-2xl bg-gradient-to-br ${service.color} flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg`}
              >
                <span className="text-black">{service.icon}</span>
              </div>
              {/* Title */}
              <h4 className="text-xl sm:text-2xl font-bold text-white mb-3 group-hover:text-[#FCA311] transition-colors leading-tight">
                {service.title}
              </h4>
              {/* Description */}
              <p className="text-gray-300 leading-relaxed mb-5 flex-grow text-sm sm:text-base">
                {service.desc}
              </p>
              {/* Features List */}
              <div className="space-y-2 mb-6">
                {service.features.map((feature, idx) => (
                  <div
                    key={idx}
                    className="flex items-center gap-2 text-xs sm:text-sm text-gray-400"
                  >
                    <div
                      className={`w-1.5 h-1.5 rounded-full bg-gradient-to-r ${service.color}`}
                    />
                    {feature}
                  </div>
                ))}
              </div>
              {/* CTA Button */}
              <a
                href={service.link}
                target="_blank"
                rel="noopener noreferrer"
                className={`group/btn relative bg-gradient-to-r ${service.color} text-black font-bold py-2.5 px-5 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 flex items-center justify-center gap-2 mt-auto text-sm sm:text-base`}
              >
                <span>Explore Now</span>
                <FaArrowRight className="text-xs sm:text-sm group-hover/btn:translate-x-1 transition-transform" />
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover/btn:opacity-100 transition-opacity" />
              </a>
            </div>
            {/* Card Hover Ripple */}
            <div className="absolute inset-0 pointer-events-none">
              <div
                className={`absolute w-1/2 h-1/2 rounded-full bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-10 blur-2xl`}
                style={{ right: "-10%", bottom: "-10%" }}
              />
            </div>
          </div>
        ))}
      </div>

      {/* Stats Section */}
      <div className="mt-16 md:mt-24 grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8 max-w-4xl mx-auto">
        <div className="text-center">
          <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-[#FCA311] to-[#FF7E5F] bg-clip-text text-transparent mb-1 md:mb-2">
            50K+
          </div>
          <div className="text-gray-400 text-xs md:text-sm">
            Mock Interviews
          </div>
        </div>
        <div className="text-center">
          <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-[#61DAFB] to-[#4FC3F7] bg-clip-text text-transparent mb-1 md:mb-2">
            98%
          </div>
          <div className="text-gray-400 text-xs md:text-sm">Success Rate</div>
        </div>
        <div className="text-center">
          <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-[#FF7E5F] to-[#FEB47B] bg-clip-text text-transparent mb-1 md:mb-2">
            1000+
          </div>
          <div className="text-gray-400 text-xs md:text-sm">
            Certified Interviewers
          </div>
        </div>
        <div className="text-center">
          <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-[#FEB47B] to-[#FFD93D] bg-clip-text text-transparent mb-1 md:mb-2">
            24/7
          </div>
          <div className="text-gray-400 text-xs md:text-sm">
            Support Available
          </div>
        </div>
      </div>
    </div>
  </section>
);

export default Services;
