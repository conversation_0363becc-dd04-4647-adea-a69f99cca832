// Test Management Validation Utilities

/**
 * Test status definitions
 */
export const TEST_STATUS = {
  DRAFT: 'draft',
  SCHEDULED: 'scheduled', 
  ACTIVE: 'active',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
};

/**
 * Check if a test can be modified
 * @param {Object} test - Test object
 * @returns {Object} - { canModify: boolean, reason: string }
 */
export const canModifyTest = (test) => {
  if (!test) {
    return { canModify: false, reason: 'Test not found' };
  }

  const now = new Date();
  const scheduledDate = new Date(test.scheduledDate);
  const endDate = new Date(test.endDate);

  // Check if test is active or completed
  if (test.status === TEST_STATUS.ACTIVE) {
    return { canModify: false, reason: 'Cannot modify active test' };
  }

  if (test.status === TEST_STATUS.COMPLETED) {
    return { canModify: false, reason: 'Cannot modify completed test' };
  }

  // Check if test has started
  if (now >= scheduledDate && test.status !== TEST_STATUS.DRAFT) {
    return { canModify: false, reason: 'Cannot modify test that has already started' };
  }

  // Check if test has participants who have started
  if (test.participants && test.participants.some(p => p.status === 'started' || p.status === 'completed')) {
    return { canModify: false, reason: 'Cannot modify test with participants who have started' };
  }

  return { canModify: true, reason: null };
};

/**
 * Check if questions can be added to a test
 * @param {Object} test - Test object
 * @param {Array} questionIds - Array of question IDs to add
 * @returns {Object} - { canAdd: boolean, reason: string, warnings: Array }
 */
export const canAddQuestionsToTest = (test, questionIds = []) => {
  const modifyCheck = canModifyTest(test);
  if (!modifyCheck.canModify) {
    return { canAdd: false, reason: modifyCheck.reason, warnings: [] };
  }

  const warnings = [];

  // Check for duplicate questions
  const existingQuestionIds = (test.questions || []).map(q => q.questionId || q._id);
  const duplicates = questionIds.filter(id => existingQuestionIds.includes(id));
  
  if (duplicates.length > 0) {
    warnings.push(`${duplicates.length} question(s) already exist in this test`);
  }

  // Check test duration vs number of questions
  const totalQuestions = (test.questions || []).length + questionIds.length - duplicates.length;
  const estimatedTime = totalQuestions * 2; // Assume 2 minutes per question
  
  if (estimatedTime > test.duration) {
    warnings.push(`Estimated time (${estimatedTime}min) exceeds test duration (${test.duration}min)`);
  }

  return { canAdd: true, reason: null, warnings };
};

/**
 * Check if candidates can be assigned to a test
 * @param {Object} test - Test object
 * @param {Array} candidateIds - Array of candidate IDs to assign
 * @returns {Object} - { canAssign: boolean, reason: string, warnings: Array }
 */
export const canAssignCandidatesToTest = (test, candidateIds = []) => {
  if (!test) {
    return { canAssign: false, reason: 'Test not found', warnings: [] };
  }

  const warnings = [];
  const now = new Date();
  const scheduledDate = new Date(test.scheduledDate);
  const endDate = new Date(test.endDate);

  // Check if test is cancelled
  if (test.status === TEST_STATUS.CANCELLED) {
    return { canAssign: false, reason: 'Cannot assign candidates to cancelled test', warnings: [] };
  }

  // Check if test has ended
  if (now > endDate) {
    return { canAssign: false, reason: 'Cannot assign candidates to ended test', warnings: [] };
  }

  // Check for duplicate assignments
  const existingCandidateIds = (test.participants || []).map(p => p.candidateId);
  const duplicates = candidateIds.filter(id => existingCandidateIds.includes(id));
  
  if (duplicates.length > 0) {
    warnings.push(`${duplicates.length} candidate(s) already assigned to this test`);
  }

  // Check if test is starting soon
  const hoursUntilStart = (scheduledDate - now) / (1000 * 60 * 60);
  if (hoursUntilStart < 24 && hoursUntilStart > 0) {
    warnings.push('Test starts in less than 24 hours');
  }

  // Check if test has questions
  if (!test.questions || test.questions.length === 0) {
    warnings.push('Test has no questions assigned');
  }

  return { canAssign: true, reason: null, warnings };
};

/**
 * Validate test creation data
 * @param {Object} testData - Test data object
 * @returns {Object} - { isValid: boolean, errors: Array }
 */
export const validateTestData = (testData) => {
  const errors = [];

  // Required fields
  if (!testData.testName || !testData.testName.trim()) {
    errors.push('Test name is required');
  }

  if (!testData.scheduledDate) {
    errors.push('Scheduled date is required');
  }

  if (!testData.endDate) {
    errors.push('End date is required');
  }

  if (!testData.duration || testData.duration <= 0) {
    errors.push('Duration must be greater than 0');
  }

  if (!testData.passingScore || testData.passingScore < 0 || testData.passingScore > 100) {
    errors.push('Passing score must be between 0 and 100');
  }

  // Date validations
  if (testData.scheduledDate && testData.endDate) {
    const scheduledDate = new Date(testData.scheduledDate);
    const endDate = new Date(testData.endDate);
    const now = new Date();

    if (scheduledDate >= endDate) {
      errors.push('End date must be after scheduled date');
    }

    if (scheduledDate < now) {
      errors.push('Scheduled date cannot be in the past');
    }

    const testDurationMs = endDate - scheduledDate;
    const testDurationMinutes = testDurationMs / (1000 * 60);
    
    if (testDurationMinutes < testData.duration) {
      errors.push('Test duration exceeds the time between scheduled and end dates');
    }
  }

  // Questions validation
  if (testData.questions && testData.questions.length > 0) {
    const invalidQuestions = testData.questions.filter(q => !q.questionId && !q._id);
    if (invalidQuestions.length > 0) {
      errors.push(`${invalidQuestions.length} question(s) have invalid IDs`);
    }
  }

  // Assignment validation
  if ((!testData.associatedJobs || testData.associatedJobs.length === 0) && 
      (!testData.participants || testData.participants.length === 0)) {
    errors.push('Test must be assigned to at least one job or candidate');
  }

  return { isValid: errors.length === 0, errors };
};

/**
 * Validate question bundle data
 * @param {Object} bundleData - Bundle data object
 * @returns {Object} - { isValid: boolean, errors: Array }
 */
export const validateQuestionBundleData = (bundleData) => {
  const errors = [];

  // Required fields
  if (!bundleData.bundleName || !bundleData.bundleName.trim()) {
    errors.push('Bundle name is required');
  }

  if (!bundleData.questionIds || !Array.isArray(bundleData.questionIds) || bundleData.questionIds.length === 0) {
    errors.push('At least one question ID is required');
  }

  // Validate question IDs
  if (bundleData.questionIds) {
    const invalidIds = bundleData.questionIds.filter(id => !id || typeof id !== 'string');
    if (invalidIds.length > 0) {
      errors.push(`${invalidIds.length} invalid question ID(s) found`);
    }
  }

  // Validate difficulty
  const validDifficulties = ['Easy', 'Medium', 'Hard'];
  if (bundleData.difficulty && !validDifficulties.includes(bundleData.difficulty)) {
    errors.push('Difficulty must be Easy, Medium, or Hard');
  }

  return { isValid: errors.length === 0, errors };
};

/**
 * Get test status with additional information
 * @param {Object} test - Test object
 * @returns {Object} - Status information
 */
export const getTestStatus = (test) => {
  if (!test) return { status: 'unknown', canModify: false, warning: 'Test not found' };

  const now = new Date();
  const scheduledDate = new Date(test.scheduledDate);
  const endDate = new Date(test.endDate);
  const modifyCheck = canModifyTest(test);

  let status = test.status || TEST_STATUS.DRAFT;
  let warning = null;

  // Auto-determine status based on dates if not explicitly set
  if (now >= scheduledDate && now <= endDate && status === TEST_STATUS.SCHEDULED) {
    status = TEST_STATUS.ACTIVE;
  } else if (now > endDate && status !== TEST_STATUS.COMPLETED && status !== TEST_STATUS.CANCELLED) {
    status = TEST_STATUS.COMPLETED;
  }

  // Add warnings
  if (status === TEST_STATUS.ACTIVE) {
    warning = 'Test is currently active';
  } else if (status === TEST_STATUS.COMPLETED) {
    warning = 'Test has been completed';
  } else if (!modifyCheck.canModify) {
    warning = modifyCheck.reason;
  }

  return {
    status,
    canModify: modifyCheck.canModify,
    warning,
    isActive: status === TEST_STATUS.ACTIVE,
    isCompleted: status === TEST_STATUS.COMPLETED,
    hasStarted: now >= scheduledDate,
    hasEnded: now > endDate
  };
};
