import React, { useState, useRef } from 'react';

const courses = [
  {
    id: 'java',
    title: 'Java Programming',
    video: 'https://www.w3schools.com/html/mov_bbb.mp4', // mock video
    description: 'Learn the basics of Java programming.'
  },
  {
    id: 'c',
    title: 'C Programming',
    video: 'https://www.w3schools.com/html/movie.mp4', // mock video
    description: 'Master the fundamentals of C language.'
  },
  {
    id: 'python',
    title: 'Python Programming',
    video: 'https://www.w3schools.com/html/mov_bbb.mp4', // mock video
    description: 'Get started with Python for data science and more.'
  },
];

export default function Courses() {
  const [selected, setSelected] = useState(null);
  const [watched, setWatched] = useState(() => {
    // Load completed courses from localStorage
    const stored = localStorage.getItem('completedCourses');
    return stored ? JSON.parse(stored) : {};
  });
  const videoRef = useRef();

  const handleVideoTimeUpdate = (e) => {
    const video = e.target;
    if (video.currentTime >= video.duration - 1) {
      if (!watched[selected.id]) {
        const updated = { ...watched, [selected.id]: true };
        setWatched(updated);
        localStorage.setItem('completedCourses', JSON.stringify(updated));
        // Dispatch a custom event for certificate update
        window.dispatchEvent(new Event('courseCompleted'));
      }
    }
  };

  const handleBack = () => setSelected(null);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-[#23414c] mb-8">Courses</h1>
        {!selected ? (
          <div className="grid gap-6">
            {courses.map(course => (
              <div key={course.id} className="bg-white rounded-xl shadow p-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4 border-l-4 border-[#23414c] cursor-pointer hover:shadow-lg transition" onClick={() => setSelected(course)}>
                <div>
                  <div className="font-bold text-lg text-[#23414c] mb-1">{course.title}</div>
                  <div className="text-gray-500 text-sm mb-1">{course.description}</div>
                </div>
                <button className="bg-[#23414c] text-white px-4 py-2 rounded-full font-semibold shadow hover:bg-[#23414c]/90 transition">Start</button>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow p-6">
            <button className="mb-4 text-[#23414c] underline" onClick={handleBack}>&larr; Back to Courses</button>
            <h2 className="text-2xl font-bold mb-2 text-[#23414c]">{selected.title}</h2>
            <p className="text-gray-500 mb-4">{selected.description}</p>
            <video
              ref={videoRef}
              width="100%"
              height="320"
              controls
              onTimeUpdate={handleVideoTimeUpdate}
              className="rounded mb-4 border"
            >
              <source src={selected.video} type="video/mp4" />
              Your browser does not support the video tag.
            </video>
            <div className="flex gap-4 items-center">
              <button
                className={`px-6 py-2 rounded-full font-bold transition ${watched[selected.id] ? 'bg-[#23414c] text-white hover:bg-[#23414c]/90' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`}
                disabled={!watched[selected.id]}
                onClick={() => alert('Certificate downloaded!')}
              >
                Download Certificate
              </button>
              {!watched[selected.id] && <span className="text-xs text-gray-400">Watch the full video to unlock certificate</span>}
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 