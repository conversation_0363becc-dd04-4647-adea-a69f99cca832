import React, { useState } from 'react';
import toast from 'react-hot-toast';

// Components
import AllQuestions from './components/AllQuestions';
import QuestionUpload from './components/QuestionUpload';
import ManualQuestionForm from './components/ManualQuestionForm';
import TestInterface from './components/TestInterface';

// Hooks
import useQuestionManager from './hooks/useQuestionManager';

const Aptitude = () => {
  const [currentView, setCurrentView] = useState('upload'); // 'upload', 'questions', 'test'
  const [showManualModal, setShowManualModal] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState(null);

  // Use the question manager hook
  const {
    questions,
    loading,
    error,
    addQuestion,
    updateQuestion,
    deleteQuestion,
    deleteQuestions,
    uploadQuestionsFromFile,
    exportQuestions,
    setError
  } = useQuestionManager();

  // Get total questions count
  const getQuestionsCount = () => {
    return questions.length;
  };

  const handleEditQuestion = (question) => {
    setEditingQuestion(question);
    setShowManualModal(true);
  };

  const handleDeleteQuestion = async (questionId) => {
    if (window.confirm('Are you sure you want to delete this question?')) {
      const result = await deleteQuestion(questionId);
      if (result.success) {
        toast.success('Question deleted successfully');
      } else {
        toast.error('Failed to delete question');
      }
    }
  };

  const handleDeleteMultiple = async (questionIds) => {
    if (window.confirm(`Are you sure you want to delete ${questionIds.length} questions?`)) {
      const result = await deleteQuestions(questionIds);
      if (result.success) {
        toast.success(`Deleted ${questionIds.length} questions`);
      } else {
        toast.error('Failed to delete questions');
      }
    }
  };

  const handleExport = () => {
    const result = exportQuestions();
    if (result.success) {
      toast.success('Questions exported successfully');
    } else {
      toast.error(result.error || 'Failed to export questions');
    }
  };

  const handleTestComplete = (results) => {
    console.log('Test completed with results:', results);
    toast.success(`Test completed! Score: ${results.score}%`);
  };

  const handleCloseModal = () => {
    setShowManualModal(false);
    setEditingQuestion(null);
  };

  // Handle file upload
  const handleFileUpload = async (file) => {
    try {
      const result = await uploadQuestionsFromFile(file);
      return result;
    } catch (error) {
      toast.error('An error occurred while uploading the file');
      return { success: false, error: error.message };
    }
  };

  // Handle manual question submission
  const handleManualSubmit = async (questionData) => {
    try {
      if (editingQuestion) {
        const result = await updateQuestion(editingQuestion._id || editingQuestion.id, questionData);
        if (result.success) {
          setEditingQuestion(null);
          return result;
        }
        return result;
      } else {
        const result = await addQuestion(questionData);
        return result;
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // Navigation functions
  const handleViewChange = (view) => {
    setCurrentView(view);
  };

  const handleAddQuestionClick = () => {
    setEditingQuestion(null);
    setShowManualModal(true);
  };

  // Render different views based on current state
  const renderMainContent = () => {
    switch (currentView) {
      case 'questions':
        return (
          <AllQuestions
            onEditQuestion={handleEditQuestion}
            onDeleteQuestion={handleDeleteQuestion}
            onDeleteMultiple={handleDeleteMultiple}
            onExport={handleExport}
            onAddQuestion={handleAddQuestionClick}
          />
        );
      case 'test':
        return (
          <TestInterface
            questions={questions}
            selectedJob={null}
            onTestComplete={handleTestComplete}
            timePerQuestion={60}
            showResults={true}
          />
        );
      default:
        return (
          <div className="space-y-6">
            {/* Question Upload and Manual Add Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex flex-col md:flex-row gap-8 justify-center">
                {/* Upload Card */}
                <div className="flex-1">
                  <QuestionUpload
                    onUpload={handleFileUpload}
                    loading={loading}
                    className="w-full"
                  />
                </div>

                {/* OR Divider */}
                <div className="hidden md:flex flex-col justify-center items-center px-4">
                  <div className="w-px h-16 bg-gray-200"></div>
                  <span className="text-gray-400 font-medium text-sm bg-white px-2 -my-2">OR</span>
                  <div className="w-px h-16 bg-gray-200"></div>
                </div>

                {/* Manual Add Card */}
                <div className="flex-1 flex flex-col items-center justify-center">
                  <div className="text-center">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Add Manually</h3>
                    <p className="text-sm text-gray-600 mb-4">Create questions one by one with custom options</p>
                    <button
                      className="px-6 py-3 bg-gradient-to-r from-[rgb(35,65,75)] to-gray-700 hover:from-[rgb(35,65,75)]/90 hover:to-gray-700/90 text-white rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl"
                      onClick={handleAddQuestionClick}
                    >
                      Add Question Manually
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Stats and Action Buttons */}
            {getQuestionsCount() > 0 && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                  <div className="text-center sm:text-left">
                    <h3 className="text-lg font-semibold text-gray-900">Question Bank</h3>
                    <p className="text-sm text-gray-600">
                      You have <span className="font-medium text-[rgb(35,65,75)]">{getQuestionsCount()}</span> questions in your bank
                    </p>
                  </div>
                  <div className="flex gap-3">
                    <button
                      onClick={() => handleViewChange('questions')}
                      className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl"
                    >
                      View All Questions
                    </button>
                    <button
                      onClick={() => handleViewChange('test')}
                      className="px-6 py-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl"
                    >
                      Start Test
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        );
    }
  };

  return (
    <div className="w-full">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-[rgb(35,65,75)] to-gray-700 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Question Management</h1>
              <p className="text-sm text-gray-600">Create and manage your question bank for assessments</p>
            </div>
          </div>
          {/* <div className="flex items-center gap-3">
            {getQuestionsCount() > 0 && (
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                {getQuestionsCount()} Questions
              </span>
            )}
          </div> */}
        </div>
      </div>

      {/* Main Content */}
      <div className="space-y-6">
        {renderMainContent()}
      </div>

      {/* Manual Question Form Modal */}
      <ManualQuestionForm
        isOpen={showManualModal}
        onClose={handleCloseModal}
        onSubmit={handleManualSubmit}
        editingQuestion={editingQuestion}
      />

      {/* Error Display */}
      {error && (
        <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50">
          <span className="block sm:inline">{error}</span>
          <button
            onClick={() => setError(null)}
            className="ml-2 text-red-700 hover:text-red-900"
          >
            ×
          </button>
        </div>
      )}
    </div>
  );
};


export default Aptitude;
