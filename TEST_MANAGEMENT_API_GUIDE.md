# Test Management API Usage Guide

## 🚀 Quick Start

### Import the Store
```javascript
import useCompanyStore from '../store/companyStore';

const {
  // Test Management
  createTest,
  getTests,
  updateTest,
  deleteTest,
  addQuestionsToTest,
  assignCandidatesToTest,
  
  // Question Management
  getQuestionCategories,
  getQuestionsByCategory,
  filterQuestions,
  
  // Question Bundles
  createQuestionBundle,
  getQuestionBundles,
  updateQuestionBundle,
  deleteQuestionBundle,
  
  // Candidate Management
  getCandidates,
  searchCandidates,
  getAvailableCandidatesForTest,
  
  // Bulk Operations
  bulkAssignCandidatesToTests,
  bulkAddQuestionsToTests,
  bulkCreateQuestionBundlesByCategory,
  
  // Validation
  validateTest,
  validateBundle,
  getTestStatusInfo
} = useCompanyStore();
```

## 📝 Test Management

### Create a Test
```javascript
const createNewTest = async () => {
  const testData = {
    testName: 'JavaScript Developer Test',
    description: 'Test for frontend developers',
    duration: 60,
    passingScore: 70,
    scheduledDate: '2024-01-15T10:00:00Z',
    endDate: '2024-01-15T12:00:00Z',
    questions: [
      { questionId: '507f1f77bcf86cd799439011', points: 2 }
    ],
    associatedJobs: ['507f1f77bcf86cd799439012'],
    instructions: 'Read carefully',
    allowedAttempts: 1,
    randomizeQuestions: true,
    showResults: false
  };

  const result = await createTest(testData);
  if (result.success !== false) {
    console.log('Test created:', result);
  } else {
    console.error('Validation errors:', result.errors);
  }
};
```

### Add Questions to Test (Fixed API)
```javascript
const addQuestions = async (testId) => {
  const questions = [
    { questionId: 'q1', points: 1 },
    { questionId: 'q2', points: 2 }
  ];

  const result = await addQuestionsToTest(testId, questions);
  if (result.success !== false) {
    console.log('Questions added:', result);
    if (result.warnings) {
      console.warn('Warnings:', result.warnings);
    }
  } else {
    console.error('Error:', result.error);
  }
};
```

### Assign Candidates to Test
```javascript
const assignCandidates = async (testId) => {
  const candidateIds = ['candidate1', 'candidate2'];

  const result = await assignCandidatesToTest(testId, candidateIds);
  if (result.success !== false) {
    console.log('Candidates assigned:', result);
    if (result.warnings) {
      console.warn('Warnings:', result.warnings);
    }
  } else {
    console.error('Error:', result.error);
  }
};
```

## 🔍 Question Management

### Get Question Categories
```javascript
const loadCategories = async () => {
  const result = await getQuestionCategories();
  console.log('Categories:', result.categories);
  // Output: [{ category: 'Frontend', count: 15 }, { category: 'Backend', count: 20 }]
};
```

### Filter Questions
```javascript
const searchQuestions = async () => {
  const filters = {
    searchTerm: 'javascript',
    category: 'Frontend',
    difficulty: 'Medium',
    page: 1,
    limit: 10
  };

  const result = await filterQuestions(filters);
  console.log('Filtered questions:', result.questions);
  console.log('Pagination:', result.pagination);
};
```

### Get Questions by Category
```javascript
const getByCategory = async () => {
  const result = await getQuestionsByCategory('Frontend');
  console.log('Frontend questions:', result.questions);
};
```

## 📦 Question Bundle Management

### Create Question Bundle
```javascript
const createBundle = async () => {
  const bundleData = {
    bundleName: 'React Basics Bundle',
    description: 'Basic React questions',
    category: 'Frontend',
    difficulty: 'Medium',
    questionIds: ['q1', 'q2', 'q3'],
    tags: ['react', 'javascript']
  };

  const result = await createQuestionBundle(bundleData);
  if (result.success !== false) {
    console.log('Bundle created:', result.bundle);
  } else {
    console.error('Validation errors:', result.errors);
  }
};
```

### Get Question Bundles
```javascript
const loadBundles = async () => {
  const result = await getQuestionBundles({ 
    page: 1, 
    limit: 10, 
    category: 'Frontend' 
  });
  console.log('Bundles:', result.bundles);
  console.log('Pagination:', result.pagination);
};
```

## 👥 Candidate Management

### Search Candidates
```javascript
const searchForCandidates = async () => {
  const result = await searchCandidates('john', {
    experience: '2-5',
    location: 'bangalore',
    page: 1
  });
  console.log('Search results:', result.candidates);
  console.log('Pagination:', result.pagination);
};
```

### Get Available Candidates for Test
```javascript
const getAvailableCandidates = async (testId) => {
  const result = await getAvailableCandidatesForTest(testId);
  console.log('Available candidates:', result.candidates);
  console.log('Test info:', result.testName, result.associatedJobs);
};
```

## 🔄 Bulk Operations

### Bulk Assign Candidates to Multiple Tests
```javascript
const bulkAssignCandidates = async () => {
  const testIds = ['test1', 'test2', 'test3'];
  const candidateIds = ['candidate1', 'candidate2'];

  const result = await bulkAssignCandidatesToTests(testIds, candidateIds);
  console.log('Bulk assignment results:', result.results);
  console.log(`Assigned ${result.totalCandidates} candidates to ${result.totalTests} tests`);
};
```

### Bulk Add Questions to Multiple Tests
```javascript
const bulkAddQuestions = async () => {
  const testIds = ['test1', 'test2'];
  const questions = [
    { questionId: 'q1', points: 1 },
    { questionId: 'q2', points: 2 }
  ];

  const result = await bulkAddQuestionsToTests(testIds, questions);
  console.log('Bulk question addition results:', result.results);
};
```

### Bulk Create Question Bundles by Category
```javascript
const bulkCreateBundles = async () => {
  const categories = ['Frontend', 'Backend', 'Database'];
  const result = await bulkCreateQuestionBundlesByCategory(categories, 'Auto Bundle');
  console.log(`Created ${result.totalBundles} bundles:`, result.results);
};
```

## ✅ Validation

### Validate Test Data
```javascript
const validateTestData = () => {
  const testData = {
    testName: 'Sample Test',
    duration: 60,
    passingScore: 70,
    scheduledDate: '2024-01-15T10:00:00Z',
    endDate: '2024-01-15T12:00:00Z',
    associatedJobs: ['job1']
  };

  const validation = validateTest(testData);
  if (validation.isValid) {
    console.log('Test data is valid');
  } else {
    console.error('Validation errors:', validation.errors);
  }
};
```

### Check Test Status and Modification Permissions
```javascript
const checkTestStatus = (test) => {
  const status = getTestStatusInfo(test);
  console.log('Test status:', status.status);
  console.log('Can modify:', status.canModify);
  console.log('Warning:', status.warning);
  console.log('Is active:', status.isActive);
  console.log('Has ended:', status.hasEnded);
};
```

## 🛠️ Error Handling

### Graceful Error Handling
```javascript
const handleApiCall = async () => {
  try {
    const result = await someApiFunction();
    
    // Check for validation errors
    if (result.success === false) {
      if (result.errors) {
        // Handle validation errors
        result.errors.forEach(error => console.error(error));
      } else {
        // Handle general error
        console.error(result.error);
      }
      return;
    }

    // Handle warnings
    if (result.warnings && result.warnings.length > 0) {
      result.warnings.forEach(warning => console.warn(warning));
    }

    // Success
    console.log('Operation successful:', result);
  } catch (error) {
    console.error('Unexpected error:', error);
  }
};
```

## 🧪 Testing

### Use the Test Components
```javascript
// Import test components
import TestAPIDemo from './components/TestAPIDemo';
import ReactErrorFixes from './components/ReactErrorFixes';

// Use in your app for testing
<TestAPIDemo />
<ReactErrorFixes />
```

### Run Integration Tests
```bash
npm test src/tests/testManagementIntegration.test.js
```

## 📋 Response Formats

### Standard Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { /* response data */ },
  "warnings": ["Optional warning messages"]
}
```

### Validation Error Response
```json
{
  "success": false,
  "errors": ["Error message 1", "Error message 2"]
}
```

### API Error Response
```json
{
  "success": false,
  "error": "API error message"
}
```

This guide covers all the implemented test management APIs with practical examples and error handling patterns.
