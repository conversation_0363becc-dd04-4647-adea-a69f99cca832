import React, { useContext, useEffect, useState } from 'react';
// import { SharedDataContext } from '../../App';
import { Users, Building2, FileText, Settings, Activity, AlertTriangle, CheckCircle, Clock, TrendingUp, BarChart3 } from 'lucide-react';
import { Outlet, NavLink, useLocation } from 'react-router-dom';
import { motion, AnimatePresence, useMotionValue, useTransform, animate } from 'framer-motion';
import { FaUserCircle, FaBuilding, FaClipboardList, FaUsersCog, FaCog, FaTrash, FaChartLine, FaExclamationTriangle } from 'react-icons/fa';
import AdminUsers from "./AdminUsers.jsx";
// useAuth is used by the imported components
import { SidebarUserInfo } from '../../components/common/UserInfo';
import { SidebarLogoutButton } from '../../components/common/LogoutButton';
import useAdminStore from '../../store/adminStore';

// New Counter component for animating numbers
const Counter = ({ value }) => {
  const count = useMotionValue(0);
  const rounded = useTransform(count, (latest) => Math.round(latest));

  useEffect(() => {
    const controls = animate(count, value, { duration: 1.5, ease: "easeOut" });
    return controls.stop;
  }, [value]);

  return <motion.div className="text-3xl font-extrabold text-[rgb(35,65,75)]">{rounded}</motion.div>;
};

const AdminDashboard = () => {
  const location = useLocation();
  // const { companies, addCompany, jobPosts, addJobPost, setCompanies, setJobPosts } = useContext(SharedDataContext);
  const [showCompanies, setShowCompanies] = useState(false);
  const [showJobPosts, setShowJobPosts] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false); // For mobile sidebar
  // Auth hook is used by the imported components

  // User profile for header
  const [profile, setProfile] = useState(() => {
    const stored = localStorage.getItem('adminUserProfile');
    return stored ? JSON.parse(stored) : null;
  });
  React.useEffect(() => {
    const stored = localStorage.getItem('adminUserProfile');
    if (stored) setProfile(JSON.parse(stored));
  }, [localStorage.getItem('adminUserProfile')]);
  const latestUser = profile;
  const [showProfile, setShowProfile] = useState(false);

  // Animation variants
  const listVariants = {
    visible: { transition: { staggerChildren: 0.12 } },
    hidden: {},
  };
  const cardVariants = {
    hidden: { opacity: 0, y: 24 },
    visible: { opacity: 1, y: 0, transition: { type: 'spring', stiffness: 60 } },
    exit: { opacity: 0, y: 24, transition: { duration: 0.15 } },
  };

  // Show lists only on /admin-dashboard (not on child routes)
  const isDashboardRoot = location.pathname === '/admin-dashboard';
  const hideSidebar = [
    '/admin-dashboard/companies',
    '/admin-dashboard/companies/add',
    '/admin-dashboard/job-posts',
    '/admin-dashboard/job-posts/add', // Add this new route
    '/admin-dashboard/users',
    '/admin-dashboard/settings'
  ].includes(location.pathname);

  // Delete handlers
  const handleDeleteCompany = (idx) => {
    const updated = companies.filter((_, i) => i !== idx);
    setCompanies(updated);
    localStorage.setItem('companies', JSON.stringify(updated));
  };
  const handleDeleteJobPost = (idx) => {
    const updated = jobPosts.filter((_, i) => i !== idx);
    setJobPosts(updated);
    localStorage.setItem('jobPosts', JSON.stringify(updated));
  };

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Header */}
      <header className="flex-shrink-0 fixed top-0 left-0 w-full z-50 shadow h-16 sm:h-20 flex items-center border-b border-gray-200 justify-between px-4 sm:px-12" style={{ background: 'rgb(35, 65, 75)' }}>
        {/* Hamburger for mobile */}
        {!hideSidebar && (
          <button className="sm:hidden mr-2 text-white focus:outline-none" onClick={() => setSidebarOpen(true)} aria-label="Open sidebar">
            <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="2.5" viewBox="0 0 24 24"><path d="M4 6h16M4 12h16M4 18h16" /></svg>
          </button>
        )}
        <h1 className="text-2xl sm:text-3xl font-extrabold text-white ml-2 sm:ml-12 tracking-tight cursor-pointer" onClick={() => window.location.href = '/admin-dashboard'}>Prayoj</h1>
        <div className="relative flex items-center mr-2 sm:mr-12">
          {latestUser ? (
            <>
              <button
                className="w-12 h-12 rounded-full bg-white flex items-center justify-center text-[rgb(35,65,75)] font-bold text-xl shadow hover:ring-2 hover:ring-blue-400 transition focus:outline-none"
                onClick={() => setShowProfile((v) => !v)}
                title={latestUser.name}
              >
                {latestUser.name ? latestUser.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2) : <FaUserCircle className="text-2xl" />}
              </button>
              {showProfile && (
                <div className="absolute right-0 top-full mt-2 w-64 bg-white rounded-xl shadow-lg p-5 z-50 border border-gray-100">
                  <div className="flex flex-col items-center gap-2">
                    <div className="w-14 h-14 rounded-full bg-[rgb(35,65,75)] flex items-center justify-center text-white font-bold text-2xl mb-2">
                      {latestUser.name ? latestUser.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2) : <FaUserCircle className="text-2xl" />}
                    </div>
                    <div className="font-bold text-[rgb(35,65,75)] text-lg">{latestUser.name}</div>
                    <div className="text-gray-700 text-sm">{latestUser.email}</div>
                    <div className="text-gray-500 text-xs mt-1">{latestUser.role}</div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <FaUserCircle className="text-white text-3xl" />
          )}
        </div>
      </header>

      <div className="flex flex-1 pt-16 sm:pt-20 overflow-hidden">
        {/* Sidebar */}
        {!hideSidebar && (
          <>
            {/* Desktop sidebar */}
            <aside className="hidden sm:flex flex-col w-64 bg-gradient-to-b from-[rgb(35,65,75)] to-gray-900 border-r border-gray-900 shadow-2xl flex-shrink-0 overflow-y-auto">
              {/* Admin Header with User Info */}
              <div className="px-6 py-6 border-b border-gray-800">
                <SidebarUserInfo />
              </div>
              <nav className="flex flex-col gap-4 px-4 py-8 custom-scrollbar">
                <NavLink to="/admin-dashboard/users" className={({ isActive }) => isActive ? "flex items-center gap-3 text-xl font-bold text-white bg-white/10 shadow-lg scale-[1.03] rounded-full px-8 py-4 transition-all duration-200" : "flex items-center gap-3 text-xl font-medium text-gray-200 hover:bg-white/5 hover:text-white rounded-full px-8 py-4 transition-all duration-200"}>
                  <Users size={20} />
                  <span className="text-lg">Users</span>
                </NavLink>
                <NavLink to="/admin-dashboard/companies" className={({ isActive }) => isActive ? "flex items-center gap-3 text-lg font-bold text-white bg-white/10 shadow-lg ring-2 ring-blue-400/40 scale-[1.03] rounded-full px-6 py-3 transition-all duration-200" : "flex items-center gap-3 text-lg font-medium text-gray-200 hover:bg-white/5 hover:text-white rounded-full px-6 py-3 transition-all duration-200"}>
                  <Building2 size={20} />
                  <span className="text-lg">Companies</span>
                </NavLink>
                <NavLink to="/admin-dashboard/job-posts" className={({ isActive }) => isActive ? "flex items-center gap-3 text-lg font-bold text-white bg-white/10 shadow-lg ring-2 ring-blue-400/40 scale-[1.03] rounded-full px-6 py-3 transition-all duration-200" : "flex items-center gap-3 text-lg font-medium text-gray-200 hover:bg-white/5 hover:text-white rounded-full px-6 py-3 transition-all duration-200"}>
                  <FileText size={20} />
                  <span className="text-lg">Job Posts</span>
                </NavLink>
                <NavLink to="/admin-dashboard/settings" className={({ isActive }) => isActive ? "flex items-center gap-3 text-lg font-bold text-white bg-white/10 shadow-lg ring-2 ring-green-400/40 scale-[1.03] rounded-full px-6 py-3 transition-all duration-200" : "flex items-center gap-3 text-lg font-medium text-gray-200 hover:bg-white/5 hover:text-white rounded-full px-6 py-3 transition-all duration-200"}>
                  <Settings size={20} />
                  <span className="text-lg">Settings</span>
                </NavLink>
              </nav>

              {/* Logout Button */}
              <div className="mt-auto p-4 border-t border-gray-800">
                <SidebarLogoutButton />
              </div>
            </aside>

            {/* Mobile sidebar overlay (already fixed, no changes needed) */}
            <div className={`fixed inset-0 z-40 bg-black bg-opacity-40 transition-opacity duration-300 ${sidebarOpen ? 'block' : 'hidden'} sm:hidden`} onClick={() => setSidebarOpen(false)}></div>

            {/* Mobile sidebar */}
            <aside className={`fixed top-0 left-0 z-50 w-64 h-full bg-gradient-to-b from-[rgb(35,65,75)] to-gray-900 shadow-2xl flex flex-col p-0 transform transition-transform duration-300 ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} sm:hidden`}>
              {/* Mobile Admin Header with User Info */}
              <div className="px-6 py-6 border-b border-gray-800">
                <SidebarUserInfo />
              </div>
              <nav className="flex flex-col gap-4 px-4 py-8 custom-scrollbar">
                <NavLink to="/admin-dashboard/users" className={({ isActive }) => isActive ? "flex items-center gap-3 text-xl font-bold text-white bg-white/10 shadow-lg scale-[1.03] rounded-full px-8 py-4 transition-all duration-200" : "flex items-center gap-3 text-xl font-medium text-gray-200 hover:bg-white/5 hover:text-white rounded-full px-8 py-4 transition-all duration-200"}>
                  <Users size={20} />
                  <span className="text-lg">Users</span>
                </NavLink>
                <NavLink to="/admin-dashboard/companies" className={({ isActive }) => isActive ? "flex items-center gap-3 text-lg font-bold text-white bg-white/10 shadow-lg ring-2 ring-blue-400/40 scale-[1.03] rounded-full px-6 py-3 transition-all duration-200" : "flex items-center gap-3 text-lg font-medium text-gray-200 hover:bg-white/5 hover:text-white rounded-full px-6 py-3 transition-all duration-200"}>
                  <Building2 size={20} />
                  <span className="text-lg">Companies</span>
                </NavLink>
                <NavLink to="/admin-dashboard/job-posts" className={({ isActive }) => isActive ? "flex items-center gap-3 text-lg font-bold text-white bg-white/10 shadow-lg ring-2 ring-blue-400/40 scale-[1.03] rounded-full px-6 py-3 transition-all duration-200" : "flex items-center gap-3 text-lg font-medium text-gray-200 hover:bg-white/5 hover:text-white rounded-full px-6 py-3 transition-all duration-200"}>
                  <FileText size={20} />
                  <span className="text-lg">Job Posts</span>
                </NavLink>
                <NavLink to="/admin-dashboard/settings" className={({ isActive }) => isActive ? "flex items-center gap-3 text-lg font-bold text-white bg-white/10 shadow-lg ring-2 ring-green-400/40 scale-[1.03] rounded-full px-6 py-3 transition-all duration-200" : "flex items-center gap-3 text-lg font-medium text-gray-200 hover:bg-white/5 hover:text-white rounded-full px-6 py-3 transition-all duration-200"}>
                  <Settings size={20} />
                  <span className="text-lg">Settings</span>
                </NavLink>
              </nav>

              {/* Mobile Logout Button */}
              <div className="mt-auto p-4 border-t border-gray-800">
                <SidebarLogoutButton
                  onLogoutComplete={() => setSidebarOpen(false)}
                />
              </div>
            </aside>
          </>
        )}

        {/* Main content */}
        <main className="flex-1 overflow-y-auto">
          <div className="p-2 sm:p-6">
            {hideSidebar && (
              <div className="mb-6">
                <NavLink to="/admin-dashboard" className="inline-flex items-center gap-2 px-4 py-2 bg-[rgb(35,65,75)] text-white rounded-lg shadow hover:bg-[rgb(45,85,100)] font-semibold transition">
                  <svg width="22" height="22" fill="none" stroke="currentColor" strokeWidth="2.2" viewBox="0 0 24 24"><path d="M15 19l-7-7 7-7" /></svg>
                  Back to Dashboard
                </NavLink>
              </div>
            )}
            {isDashboardRoot ? (
              <div>
                <h1 className="text-2xl sm:text-4xl md:text-5xl font-extrabold text-center mb-6 sm:mb-10 text-[rgb(35,65,75)] drop-shadow-lg tracking-tight">Admin Dashboard</h1>
                {/* --- Summary Cards --- */}
                <motion.div
                  className="w-full max-w-4xl mx-auto flex flex-col sm:flex-row justify-center gap-4 sm:gap-8 mb-8 sm:mb-12"
                  variants={listVariants}
                  initial="hidden"
                  animate="visible"
                >
                  <motion.div
                    className="bg-white rounded-2xl shadow-xl p-7 flex items-center gap-5 flex-1 min-w-[220px] hover:shadow-2xl transition group"
                    variants={cardVariants}
                  >
                    <span className="w-14 h-14 flex items-center justify-center rounded-full bg-gradient-to-br from-blue-200 via-blue-400 to-blue-600 text-white text-3xl font-bold shadow group-hover:scale-110 transition-transform duration-300">
                      <FaBuilding />
                    </span>
                    <div>
                      {/* <Counter value={companies.length} /> */}
                      <div className="text-gray-600 font-semibold">Total Companies</div>
                    </div>
                  </motion.div>
                  <motion.div
                    className="bg-white rounded-2xl shadow-xl p-7 flex items-center gap-5 flex-1 min-w-[220px] hover:shadow-2xl transition group"
                    variants={cardVariants}
                  >
                    <span className="w-14 h-14 flex items-center justify-center rounded-full bg-gradient-to-br from-green-200 via-green-400 to-green-600 text-white text-3xl font-bold shadow group-hover:scale-110 transition-transform duration-300">
                      <FaClipboardList />
                    </span>
                    <div>
                      {/* <Counter value={jobPosts.length} /> */}
                      <div className="text-gray-600 font-semibold">Total Job Posts</div>
                    </div>
                  </motion.div>
                </motion.div>
                {/* Company List Accordion Card */}
                <motion.div
                  layout
                  className="mb-6 sm:mb-10"
                  variants={listVariants}
                  initial="hidden"
                  animate="visible"
                  transition={{ layout: { duration: 0.3, type: "spring", stiffness: 100 } }}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <span className="w-9 h-9 flex items-center justify-center rounded-full bg-blue-100 text-[rgb(35,65,75)] text-xl"><FaBuilding /></span>
                    <span className="text-xl font-bold text-[rgb(35,65,75)]">Company List</span>
                  </div>
                  <motion.button
                    layout
                    className="w-full bg-white rounded-xl shadow p-5 flex items-center justify-between text-left hover:bg-gray-100 focus:outline-none transition group"
                    onClick={() => setShowCompanies((prev) => !prev)}
                    variants={cardVariants}
                  >
                    <span className="font-bold text-lg text-[rgb(35,65,75)]">{showCompanies ? 'Hide' : 'Show'} Companies</span>
                    <svg width="28" height="28" fill="none" stroke="#64748b" strokeWidth="2.5" viewBox="0 0 24 24">
                      {showCompanies ? (
                        <path d="M6 15l6-6 6 6" />
                      ) : (
                        <path d="M6 9l6 6 6-6" />
                      )}
                    </svg>
                  </motion.button>
                  <AnimatePresence>
                    {showCompanies && (
                      <motion.div
                        className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 bg-white rounded-xl p-2 sm:p-4 shadow-inner"
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        {companies.length === 0 ? (
                          <motion.div className="text-gray-500 col-span-2" key="empty-company" variants={cardVariants}>
                            No companies added yet.
                          </motion.div>
                        ) : (
                          companies.map((company, idx) => (
                            <motion.div
                              key={idx}
                              className="bg-gradient-to-br from-blue-50 via-white to-blue-100 rounded-lg shadow p-5 flex flex-col gap-2 relative border border-blue-100 hover:border-blue-400 transition"
                              variants={cardVariants}
                              initial="hidden"
                              animate="visible"
                              exit="exit"
                            >
                              <button onClick={() => handleDeleteCompany(idx)} className="absolute top-3 right-3 text-red-500 hover:text-red-700 bg-white rounded-full p-2 shadow transition" title="Delete">
                                <FaTrash />
                              </button>
                              <span className="absolute top-2 right-4 text-3xl font-black text-blue-200 opacity-30 select-none">{idx + 1}</span>
                              <div className="font-semibold text-lg text-[rgb(35,65,75)]">{company.name}</div>
                              <div className="text-sm text-gray-600">{company.email}</div>
                              <div className="text-sm text-gray-600">{company.address}</div>
                              <div className="text-sm text-gray-600">Mode: {company.mode}</div>
                              <div className="text-sm text-gray-600">Timing: {company.timing}</div>
                            </motion.div>
                          ))
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
                {/* Job Posts List Accordion Card */}
                <motion.div
                  layout
                  className="mb-6 sm:mb-10"
                  variants={listVariants}
                  initial="hidden"
                  animate="visible"
                  transition={{ layout: { duration: 0.3, type: "spring", stiffness: 100 } }}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <span className="w-9 h-9 flex items-center justify-center rounded-full bg-green-100 text-[rgb(35,65,75)] text-xl"><FaClipboardList /></span>
                    <span className="text-xl font-bold text-[rgb(35,65,75)]">Job Posts List</span>
                  </div>
                  <motion.button
                    layout
                    className="w-full bg-white rounded-xl shadow p-5 flex items-center justify-between text-left hover:bg-gray-100 focus:outline-none transition group"
                    onClick={() => setShowJobPosts((prev) => !prev)}
                    variants={cardVariants}
                  >
                    <span className="font-bold text-lg text-[rgb(35,65,75)]">{showJobPosts ? 'Hide' : 'Show'} Job Posts</span>
                    <svg width="28" height="28" fill="none" stroke="#64748b" strokeWidth="2.5" viewBox="0 0 24 24">
                      {showJobPosts ? (
                        <path d="M6 15l6-6 6 6" />
                      ) : (
                        <path d="M6 9l6 6 6-6" />
                      )}
                    </svg>
                  </motion.button>
                  <AnimatePresence>
                    {showJobPosts && (
                      <motion.div
                        className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 bg-white rounded-xl p-2 sm:p-4 shadow-inner"
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        {jobPosts.length === 0 ? (
                          <motion.div className="text-gray-500 col-span-2" key="empty-job" variants={cardVariants}>
                            No job posts added yet.
                          </motion.div>
                        ) : (
                          jobPosts.map((job, idx) => (
                            <motion.div
                              key={idx}
                              className="bg-gradient-to-br from-green-50 via-white to-green-100 rounded-lg shadow p-5 flex flex-col gap-2 relative border border-green-100 hover:border-green-400 transition"
                              variants={cardVariants}
                              initial="hidden"
                              animate="visible"
                              exit="exit"
                            >
                              <button onClick={() => handleDeleteJobPost(idx)} className="absolute top-3 right-3 text-red-500 hover:text-red-700 bg-white rounded-full p-2 shadow transition" title="Delete">
                                <FaTrash />
                              </button>
                              <span className="absolute top-2 right-4 text-3xl font-black text-green-100 opacity-30 select-none">{idx + 1}</span>
                              <div className="font-semibold text-lg text-[rgb(35,65,75)]">{job.company} - {job.role}</div>
                              <div className="text-sm text-gray-600">Salary: {job.salary}</div>
                              <div className="text-sm text-gray-600">Type: {job.type}</div>
                              <div className="text-sm text-gray-600">Work Mode: {job.workMode}</div>
                            </motion.div>
                          ))
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              </div>
            ) : (
              <Outlet />
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminDashboard;
