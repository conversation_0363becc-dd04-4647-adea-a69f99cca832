import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate, Link, NavLink } from 'react-router-dom';
import { FaEye, FaMapMarkerAlt, FaArrowRight, FaUser, FaBriefcase, FaClipboardList, FaFlask, FaChartBar, FaHome, FaFileAlt, FaSpinner } from 'react-icons/fa';
import useStudentStore from '../../store/studentStore';

export default function Home() {
  const navigate = useNavigate();

  // Get data from store
  const { jobs, jobsLoading, fetchJobs } = useStudentStore();

  // Load jobs on component mount
  useEffect(() => {
    fetchJobs({ limit: 6 }); // Load only 6 jobs for home page
  }, [fetchJobs]);

  // Only show jobs with status 'open' and created in the last 7 days
  const now = new Date();
  const newJobs = jobs.filter(job =>
    job.isActive &&
    (now - new Date(job.createdAt)) < 7 * 24 * 60 * 60 * 1000
  );

  const navLinks = [
    { to: '/student/home', label: 'Home', icon: <FaHome /> },
    { to: '/student/profile', label: 'Profile', icon: <FaUser /> },
    { to: '/student/resume', label: 'Resume', icon: <FaFileAlt /> },
    { to: '/student/jobs', label: 'Jobs', icon: <FaBriefcase /> },
    { to: '/student/applications', label: 'Applications', icon: <FaClipboardList /> },
    { to: '/student/tests', label: 'Tests', icon: <FaFlask /> },
    { to: '/student/results', label: 'Results', icon: <FaChartBar /> },
  ];

  return (
    <div className="pb-20">
      {/* Hero Section */}
      <div className="flex flex-col items-center justify-center gap-8 mt-16 mb-24">
        <h1 className="text-5xl font-extrabold mb-4 text-[#23414c] text-center">Unlock Your Career</h1>
        <p className="text-lg text-gray-500 mb-8 max-w-xl text-center">
          Explore opportunities from across the globe to grow, showcase skills, gain CV points & get hired by your dream company.
        </p>
        <button className="bg-[#23414c] text-white font-bold px-8 py-3 rounded-full shadow hover:bg-[#23414c]/90 transition" onClick={() => navigate('/student/jobs')}>Search your dream job</button>
      </div>
      {/* New Jobs Section */}
      <div className="max-w-6xl mx-auto mb-16">
        <h2 className="text-2xl font-bold mb-6 text-[#23414c]">New Jobs</h2>

        {/* Loading State */}
        {jobsLoading && (
          <div className="flex items-center justify-center py-8">
            <FaSpinner className="animate-spin text-2xl text-blue-600 mr-3" />
            <span className="text-gray-600">Loading jobs...</span>
          </div>
        )}

        {/* Jobs Display */}
        {!jobsLoading && (
          <div className="flex gap-6 overflow-x-auto pb-4">
            {newJobs.length === 0 && (
              <div className="text-gray-500 p-8">No new jobs at the moment.</div>
            )}
            {newJobs.map(job => (
              <Link
                key={job._id || job.id}
                to={`/student/jobs/${job._id || job.id}`}
                className="relative min-w-[320px] max-w-xs rounded-2xl shadow-lg p-6 bg-white flex-shrink-0 hover:shadow-2xl transition cursor-pointer group border border-gray-100"
              >
                <div className="flex items-center gap-2 mb-2">
                  <span className="bg-blue-100 text-blue-800 text-xs font-bold px-3 py-1 rounded-full shadow mr-2">
                    {job.workMode || job.mode || 'Full-time'}
                  </span>
                  {job.isActive && (
                    <span className="bg-green-100 text-green-800 text-xs font-bold px-3 py-1 rounded-full shadow flex items-center gap-1">
                      ⚡ Active
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-3 mb-4">
                  {(job.companyId?.logo || job.companyLogo) ? (
                    <img
                      src={job.companyId?.logo || job.companyLogo}
                      alt={job.companyId?.name || job.companyName || job.company}
                      className="w-12 h-12 rounded-xl bg-white shadow object-cover"
                    />
                  ) : (
                    <div className="w-12 h-12 rounded-xl bg-blue-100 shadow flex items-center justify-center">
                      <FaBriefcase className="text-blue-600" />
                    </div>
                  )}
                  <span className="font-bold text-lg text-gray-800">
                    {job.companyId?.name || job.companyName || job.company}
                  </span>
                </div>
                <div className="font-bold text-xl mb-2 text-gray-900">{job.title}</div>
                <div className="flex items-center gap-3 text-gray-600 text-sm mb-2">
                  <FaEye /> {job.views || 0} Views
                </div>
                <div className="flex items-center gap-2 text-gray-600 text-sm">
                  <FaMapMarkerAlt /> {job.location}
                </div>
                {job.salary && (
                  <div className="mt-2 text-green-600 font-medium text-sm">
                    {typeof job.salary === 'object'
                      ? `${job.salary.currency || '₹'} ${job.salary.min || 0} - ${job.salary.max || 0}`
                      : job.salary
                    }
                  </div>
                )}
                <span className="absolute top-4 right-4 text-gray-400 group-hover:text-[#23414c] transition">
                  <FaArrowRight />
                </span>
              </Link>
            ))}
          </div>
        )}
      </div>
      {/* Footer Section */}
      <footer className="mt-20 bg-[#23414c] text-white py-12 px-4">
        <div className="max-w-5xl mx-auto flex flex-col md:flex-row md:justify-between gap-8">
          <div className="flex-1 flex flex-col gap-2">
            <h3 className="text-2xl font-extrabold mb-2">Medini</h3>
            <div><span className="font-bold">Email:</span> <a href="mailto:<EMAIL>" className="underline text-white"><EMAIL></a></div>
            <div><span className="font-bold">Contact:</span> +91 98765 43210</div>
            <div><span className="font-bold">Address:</span> 123, Medini Avenue, Bengaluru, Karnataka, India</div>
          </div>
          <div className="flex-1 flex items-center justify-center md:justify-end">
            <p className="text-gray-200 text-center md:text-right max-w-md">Empowering students with opportunities, learning, and real-time analytics for a brighter future.</p>
          </div>
        </div>
        <div className="text-center text-gray-300 mt-8 text-sm">&copy; {new Date().getFullYear()} Medini Student Portal. All rights reserved.</div>
      </footer>
    </div>
  );
} 