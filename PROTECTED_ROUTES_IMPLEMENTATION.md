# Protected Routes Implementation

This document describes the implementation of authentication and role-based access control for the application.

## Overview

The application now has a comprehensive protected routing system that:
- Authenticates users before allowing access to protected routes
- Implements role-based access control (RBAC) for different user types
- Provides proper redirects based on authentication status and user roles
- Maintains authentication state across page refreshes

## User Roles

The system supports three user roles:

1. **Admin** - Full administrative access
2. **Company** - Company-specific features and job management
3. **Student** - Student dashboard and test-taking features

## Components Created

### 1. Authentication Components

#### `src/components/auth/ProtectedRoute.jsx`
- Handles basic authentication checks
- Redirects unauthenticated users to login
- Shows loading state during authentication checks
- Prevents authenticated users from accessing auth pages

#### `src/components/auth/RoleBasedRoute.jsx`
- Extends ProtectedRoute with role-based access control
- Provides pre-configured route components for each role:
  - `AdminRoute` - Only accessible by admin users
  - `CompanyRoute` - Only accessible by company users
  - `StudentRoute` - Only accessible by student users
  - `AdminOrCompanyRoute` - Accessible by both admin and company users

#### `src/components/auth/AuthProvider.jsx`
- Initializes authentication state on app startup
- Shows loading screen while checking authentication
- Wraps the entire app to ensure auth state is ready

### 2. Authentication Hook

#### `src/hooks/useAuth.js`
- Custom hook that provides authentication state and methods
- Integrates with the existing authStore
- Provides navigation helpers for login/logout with redirects
- Includes role checking utilities

### 3. Navigation Components

#### `src/components/navigation/RoleBasedNavbar.jsx`
- Role-aware navigation component
- Shows different menu items based on user role
- Displays user information and logout functionality
- Handles authentication state in the UI

### 4. Updated Store

#### `src/store/authStore.js`
- Enhanced with role checking methods
- Added initialization state tracking
- Improved error handling and loading states

## Route Protection

### Public Routes (No Authentication Required)
- `/` - Landing page
- `/login` - Login page
- `/register` - Registration page
- `/verify-otp` - OTP verification
- `/auth-demo` - Demo page for testing

### Admin Routes (Admin Role Required)
- `/admin-dashboard` - Admin dashboard
- `/admin-dashboard/companies` - Company management
- `/admin-dashboard/job-posts` - Job post management
- `/admin-dashboard/users` - User management
- `/admin-dashboard/settings` - Admin settings

### Company Routes (Company Role Required)
- `/dashboard` - Company dashboard
- `/job-create` - Create job postings
- `/test-management` - Manage tests
- `/aptitude` - Question management
- `/interview` - Interview features
- `/profile` - Company profile

### Student Routes (Student Role Required)
- `/student-dashboard` - Student dashboard
- `/test` - Take tests
- `/test-result` - View test results
- `/interview-prep` - Interview preparation
- `/student-profile` - Student profile

## Usage Examples

### Basic Protected Route
```jsx
import ProtectedRoute from './components/auth/ProtectedRoute';

<Route 
  path="/protected" 
  element={
    <ProtectedRoute>
      <ProtectedComponent />
    </ProtectedRoute>
  } 
/>
```

### Role-Based Route
```jsx
import { AdminRoute, CompanyRoute } from './components/auth/RoleBasedRoute';

// Admin only
<Route 
  path="/admin-only" 
  element={
    <AdminRoute>
      <AdminComponent />
    </AdminRoute>
  } 
/>

// Company only
<Route 
  path="/company-only" 
  element={
    <CompanyRoute>
      <CompanyComponent />
    </CompanyRoute>
  } 
/>
```

### Using the Auth Hook
```jsx
import useAuth from './hooks/useAuth';

const MyComponent = () => {
  const { 
    user, 
    isAuthenticated, 
    login, 
    logout, 
    isAdmin, 
    isCompany, 
    isStudent 
  } = useAuth();

  // Component logic here
};
```

## Authentication Flow

1. **App Initialization**
   - AuthProvider checks for existing token in localStorage
   - Validates token with backend
   - Sets authentication state

2. **Route Access**
   - ProtectedRoute checks authentication status
   - RoleBasedRoute checks user role permissions
   - Redirects to appropriate page if access denied

3. **Login Process**
   - User submits credentials
   - Backend validates and returns token + user data
   - Token stored in localStorage
   - User redirected to role-appropriate dashboard

4. **Logout Process**
   - Token removed from localStorage
   - Authentication state cleared
   - User redirected to login page

## Testing

### Manual Testing
1. Start the development server: `npm run dev`
2. Visit `/auth-demo` for interactive testing
3. Test different user roles and route access
4. Verify redirects work correctly

### Test Scenarios
- Unauthenticated access to protected routes
- Role-based access restrictions
- Authentication persistence across page refreshes
- Proper redirects after login/logout
- Navigation menu updates based on role

## Security Considerations

1. **Token Storage**: Tokens are stored in localStorage (consider httpOnly cookies for production)
2. **Route Protection**: All sensitive routes are protected at the component level
3. **Role Validation**: User roles are validated on both frontend and backend
4. **Token Expiration**: Expired tokens are handled with automatic logout
5. **CSRF Protection**: Consider implementing CSRF tokens for state-changing operations

## Backend Integration

The frontend expects the following backend endpoints:

- `POST /auth/login` - User authentication
- `GET /auth/me` - Get current user info
- `POST /auth/logout` - User logout
- `POST /auth/register` - User registration
- `POST /auth/verify-otp` - OTP verification

Each endpoint should return appropriate user data including the `role` field.

## Future Enhancements

1. **Permission-Based Access**: Implement granular permissions within roles
2. **Multi-Factor Authentication**: Add 2FA support
3. **Session Management**: Implement proper session handling
4. **Audit Logging**: Track user access and actions
5. **Role Hierarchy**: Implement role inheritance (e.g., admin can access company features)

## Troubleshooting

### Common Issues

1. **Infinite Redirects**: Check for circular redirect logic in route protection
2. **Authentication Not Persisting**: Verify token storage and retrieval
3. **Role Checks Failing**: Ensure user object has correct role field
4. **Loading States**: Make sure loading states are handled properly

### Debug Tips

1. Check browser localStorage for authentication token
2. Monitor network requests for authentication endpoints
3. Use the `/auth-demo` page to test different scenarios
4. Check console for authentication errors
