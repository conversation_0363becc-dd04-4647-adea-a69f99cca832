import React, { useRef } from "react";
import { motion, useScroll, useTransform, useAnimation } from "framer-motion";

const Hero = () => {
  const imageRef = useRef(null);

  // Set up scroll progress for the image section
  const { scrollYProgress } = useScroll({
    target: imageRef,
    offset: ["start end", "end start"],
  });

  // Animate image scaling and border radius
  const scale = useTransform(scrollYProgress, [0, 0.5, 1], [1.15, 1, 0.85]);
  const borderRadius = useTransform(
    scrollYProgress,
    [0, 0.5, 1],
    ["0px", "32px", "60px"]
  );

  // Controls for staggered text animation
  const textControls = useAnimation();

  React.useEffect(() => {
    // Animate text elements when component mounts
    textControls.start((i) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: 0.4 + i * 0.2,
        duration: 1.2,
        ease: "easeOut",
      },
    }));
  }, [textControls]);

  const heroTexts = [
    "Master Interviews with <PERSON><PERSON><PERSON>",
    "Transform your career with <PERSON>yuj – the ultimate platform for interview mastery, aptitude training, and professional growth.",
  ];

  return (
    <section
      className="relative w-full h-screen flex items-center justify-center overflow-hidden bg-white"
      id="Home"
      style={{ minHeight: "100vh" }}
    >
      {/* Background Image Block */}
      <div
        ref={imageRef}
        className="absolute w-full h-full flex items-center justify-center"
        style={{
          zIndex: 1,
          top: 0,
          left: 0,
          // background: "#fae6c2", // <-- Removed background color
        }}
      >
        <motion.img
          src="./heroimage.jpg"
          alt="Hero"
          className="object-cover w-full h-full"
          style={{
            scale,
            borderRadius,
            boxShadow: "0 8px 32px 0 rgba(0,0,0,0.21)",
          }}
          transition={{
            type: "spring",
            stiffness: 100,
            // borderRadius:80,
            damping: 20,
          }}
        />
      </div>

      {/* Overlay for readability */}
      <div
        className="absolute w-full h-full"
        style={{
          // background:
          //   "linear-gradient(180deg,rgba(0,0,0,0.22) 0%,rgba(0,0,0,0.41) 100%)",
          zIndex: 2,
          top: 0,
          left: 0,
        }}
      />

      {/* Foreground Content */}
      <div
        className="relative flex flex-col items-center justify-center w-full h-full px-4"
        style={{ zIndex: 3 }}
      >
        {/* Headline */}
        <motion.h1
          className="font-extrabold text-4xl md:text-6xl text-white text-center"
          style={{
            textShadow: "0 4px 24px rgba(0,0,0,0.19)",
            fontFamily: "'Montserrat', 'Inter', 'Segoe UI', Arial, sans-serif",
            letterSpacing: "0.07em",
            lineHeight: 1.1,
          }}
          custom={0}
          initial={{ opacity: 0, y: 40 }}
          animate={textControls}
        >
          {heroTexts[0]}
        </motion.h1>

        {/* Subheadline */}
        <motion.p
          className="mt-6 max-w-2xl text-center text-white text-lg md:text-2xl font-semibold"
          style={{
            textShadow: "0 2px 12px rgba(0,0,0,0.17)",
            fontFamily: "'Inter', 'Segoe UI', Arial, sans-serif",
            fontWeight: 600,
          }}
          custom={1}
          initial={{ opacity: 0, y: 40 }}
          animate={textControls}
        >
          {heroTexts[1]}
        </motion.p>

        {/* CTA Button */}
        <motion.a
          href="#get-started"
          className="mt-8 px-8 py-3 rounded-full bg-white text-black font-bold text-lg shadow-lg hover:bg-yellow-300 transition"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
          initial={{ opacity: 0, y: 40 }}
          animate={textControls}
          custom={2}
        >
          Get Started
        </motion.a>
      </div>
    </section>
  );
};

export default Hero;
