{"name": "<PERSON><PERSON>j", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@auth0/auth0-react": "^2.3.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@lottiefiles/dotlottie-react": "^0.14.4", "animejs": "^4.0.2", "axios": "^1.10.0", "framer-motion": "^12.23.6", "gsap": "^3.13.0", "lottie-react": "^2.4.1", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-confetti": "^6.4.0", "react-countup": "^6.5.3", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-parallax-tilt": "^1.7.296", "react-router-dom": "^7.6.3", "react-select": "^5.10.2", "xlsx": "^0.18.5", "yup": "^1.6.1", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}}