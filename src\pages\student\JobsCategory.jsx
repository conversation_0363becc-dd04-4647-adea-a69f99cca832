import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { FaArrowRight, FaEye, FaMapMarkerAlt, FaBriefcase, FaSpinner } from 'react-icons/fa';
import useStudentStore from '../../store/studentStore';



export default function JobsCategory() {
  const { categoryName } = useParams();
  const { jobs, jobsLoading, fetchJobs } = useStudentStore();

  // Load jobs with category filter
  useEffect(() => {
    fetchJobs({ category: decodeURIComponent(categoryName) });
  }, [fetchJobs, categoryName]);

  const filteredJobs = jobs.filter(job =>
    job.category?.toLowerCase() === decodeURIComponent(categoryName).toLowerCase()
  );
  const [selectedJob, setSelectedJob] = useState(filteredJobs[0] || null);

  // Update selected job when filtered jobs change
  useEffect(() => {
    if (filteredJobs.length > 0 && !selectedJob) {
      setSelectedJob(filteredJobs[0]);
    }
  }, [filteredJobs, selectedJob]);

  return (
    <div className="flex h-[calc(100vh-60px)] bg-gray-50">
      {/* Left: Job List */}
      <div className="w-1/3 border-r bg-white overflow-y-auto p-4">
        <div className="mb-6 flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-1">{categoryName} Jobs</h2>
            <div className="text-gray-500">Showing jobs for category: <span className="font-semibold">{categoryName}</span></div>
          </div>
          <Link to="/student/jobs" className="text-blue-700 font-semibold flex items-center gap-2 hover:underline">Back <FaArrowRight /></Link>
        </div>
        {/* Loading State */}
        {jobsLoading && (
          <div className="flex items-center justify-center py-8">
            <FaSpinner className="animate-spin text-2xl text-blue-600 mr-3" />
            <span className="text-gray-600">Loading jobs...</span>
          </div>
        )}

        {/* Jobs List */}
        {!jobsLoading && (
          <div className="flex flex-col gap-4">
            {filteredJobs.length === 0 ? (
              <div className="text-gray-500">No jobs found for this category.</div>
            ) : (
              filteredJobs.map(job => (
                <div
                  key={job._id || job.id}
                  className={`rounded-xl p-4 shadow cursor-pointer border transition ${selectedJob && (selectedJob._id || selectedJob.id) === (job._id || job.id) ? 'border-blue-600 bg-blue-50' : 'border-gray-200 bg-white hover:border-blue-400'}`}
                  onClick={() => setSelectedJob(job)}
                >
                  <div className="flex items-center gap-3 mb-2">
                    {(job.companyId?.logo || job.logo) ? (
                      <img
                        src={job.companyId?.logo || job.logo}
                        alt={job.companyId?.name || job.company}
                        className="w-10 h-10 rounded bg-white shadow object-cover"
                      />
                    ) : (
                      <div className="w-10 h-10 rounded bg-blue-100 shadow flex items-center justify-center">
                        <FaBriefcase className="text-blue-600 text-sm" />
                      </div>
                    )}
                    <div>
                      <div className="font-bold text-lg text-gray-800">{job.title}</div>
                      <div className="text-gray-500 text-sm">{job.companyId?.name || job.company}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 text-gray-500 text-xs">
                    <FaMapMarkerAlt /> {job.location}
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
      {/* Right: Job Details */}
      <div className="flex-1 overflow-y-auto p-8">
        {selectedJob ? (
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <div className="flex items-center gap-4 mb-6">
              {(selectedJob.companyId?.logo || selectedJob.logo) ? (
                <img
                  src={selectedJob.companyId?.logo || selectedJob.logo}
                  alt={selectedJob.companyId?.name || selectedJob.company}
                  className="w-16 h-16 rounded-xl bg-white shadow object-cover"
                />
              ) : (
                <div className="w-16 h-16 rounded-xl bg-blue-100 shadow flex items-center justify-center">
                  <FaBriefcase className="text-blue-600 text-xl" />
                </div>
              )}
              <div>
                <h2 className="text-3xl font-bold text-gray-800 mb-1">{selectedJob.title}</h2>
                <div className="text-gray-500">{selectedJob.companyId?.name || selectedJob.company}</div>
              </div>
            </div>
            <div className="flex gap-8 mb-6">
              <div className="text-gray-600 flex items-center gap-2"><span>📍</span>{selectedJob.location}</div>
              <div className="text-gray-600 flex items-center gap-2"><span>👁️</span>{selectedJob.views || 0} Views</div>
            </div>
            {selectedJob.salary && (
              <div className="mb-4">
                <span className="font-semibold">Salary:</span> {
                  typeof selectedJob.salary === 'object'
                    ? `${selectedJob.salary.currency || 'INR'} ${selectedJob.salary.min || 0} - ${selectedJob.salary.max || 0}`
                    : selectedJob.salary
                }
              </div>
            )}
            {selectedJob.experienceLevel && (
              <div className="mb-4">
                <span className="font-semibold">Experience Level:</span> {selectedJob.experienceLevel}
              </div>
            )}
            {selectedJob.applicationDeadline && (
              <div className="mb-4">
                <span className="font-semibold">Application Deadline:</span> {new Date(selectedJob.applicationDeadline).toLocaleString()}
              </div>
            )}
            {selectedJob.workMode && (
              <div className="mb-4">
                <span className="font-semibold">Work Mode:</span> {selectedJob.workMode}
              </div>
            )}
            {selectedJob.jobType && (
              <div className="mb-4">
                <span className="font-semibold">Job Type:</span> {selectedJob.jobType}
              </div>
            )}
            <div className="mb-4">
              <span className="font-semibold">Job Description:</span>
              <div className="mt-2 text-gray-700">{selectedJob.description}</div>
            </div>
            {selectedJob.requirements && selectedJob.requirements.length > 0 && (
              <div className="mb-4">
                <span className="font-semibold">Requirements:</span>
                <ul className="mt-2 text-gray-700 list-disc list-inside">
                  {selectedJob.requirements.map((req, index) => (
                    <li key={index}>{req}</li>
                  ))}
                </ul>
              </div>
            )}
            {selectedJob.techStack && selectedJob.techStack.length > 0 && (
              <div className="mb-4">
                <span className="font-semibold">Tech Stack:</span>
                <div className="mt-2 flex flex-wrap gap-2">
                  {selectedJob.techStack.map((tech, index) => (
                    <span key={index} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                      {tech}
                    </span>
                  ))}
                </div>
              </div>
            )}
            <button className="bg-blue-700 text-white px-6 py-2 rounded-full font-bold mt-4 hover:bg-blue-800 transition">
              Quick Apply
            </button>
          </div>
        ) : (
          <div className="text-gray-500">Select a job to see details.</div>
        )}
      </div>
    </div>
  );
} 