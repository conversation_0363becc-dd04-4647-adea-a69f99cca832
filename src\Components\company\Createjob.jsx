// src/pages/CreateJob/CreateJob.js
import React, { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import useAuthStore from '../../store/authStore';
import useCompanyStore from '../../store/companyStore';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';
import JobFormModal from './components/JobFormModal';
import ExistingJobsSection from './components/ExistingJobsSection';
import JobCandidatesModal from './components/JobCandidatesModal';
import {
  PlusCircleIcon,
  BriefcaseIcon,
  UserGroupIcon,
  CheckCircleIcon
} from '@heroicons/react/24/solid';

const CreateJob = () => {
  const navigate = useNavigate();
  const { user, fetchCurrentUser } = useAuthStore();
  const { createJob, updateJob, getJobs, jobs, updateJobStatus } = useCompanyStore();

  const [showForm, setShowForm] = useState(false);
  const [form, setForm] = useState({
    title: '',
    description: '',
    requirements: [],
    techStack: [],
    category: '',
    jobType: 'Full-time',
    experienceLevel: 'Mid',
    salary: { min: 0, max: 0, currency: 'INR' },
    location: '',
    workMode: 'Remote',
    applicationDeadline: '',
    maxApplications: 100,
    hasTest: false,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const [selectedJob, setSelectedJob] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState('');
  const [showCandidatesModal, setShowCandidatesModal] = useState(false);
  const [selectedJobForCandidates, setSelectedJobForCandidates] = useState(null);

  const checkUserAuth = useCallback(async () => {
    try {
      setIsCheckingAuth(true);
      const token = localStorage.getItem('token');
      if (!token) return navigate('/login');

      let currentUser = user || (await fetchCurrentUser());
      if (!currentUser || currentUser.role !== 'company') return navigate('/');
    } catch {
      navigate('/login');
    } finally {
      setIsCheckingAuth(false);
    }
  }, [user, fetchCurrentUser, navigate]);

  useEffect(() => {
    checkUserAuth();
    getJobs();
  }, [checkUserAuth, getJobs]);

  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
    if (error) setError(null);
  }, [error]);

  const handleRequirementChange = (e) => {
    setForm((prev) => ({
      ...prev,
      requirements: e.target.value.split(',').map((s) => s.trim()).filter(s => s),
    }));
  };

  const handleTechStackChange = (e) => {
    setForm((prev) => ({
      ...prev,
      techStack: e.target.value.split(',').map((s) => s.trim()).filter(s => s),
    }));
  };

  const validateForm = useCallback(() => {
    const requiredFields = [
      'title',
      'description',
      'category',
      'jobType',
      'experienceLevel',
      'location',
      'workMode',
      'applicationDeadline',
    ];
    const emptyFields = requiredFields.filter((field) => !form[field]?.toString().trim());
    if (emptyFields.length > 0) return `Missing required fields: ${emptyFields.join(', ')}`;

    const { min, max } = form.salary;
    if (min < 0 || max < 0) return 'Salary cannot be negative';
    if (min >= max && max > 0) return 'Minimum salary must be less than maximum salary';
    if (form.maxApplications < 1) return 'Maximum applications must be at least 1';

    const deadline = new Date(form.applicationDeadline);
    if (deadline < new Date()) return 'Application deadline must be in the future';

    return null;
  }, [form]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      setLoading(false);
      return;
    }

    try {
      const jobData = {
        ...form,
        salary: {
          min: parseFloat(form.salary.min) || 0,
          max: parseFloat(form.salary.max) || 0,
          currency: form.salary.currency,
        },
        applicationDeadline: new Date(form.applicationDeadline).toISOString(),
        maxApplications: parseInt(form.maxApplications) || 100,
      };

      let res;
      if (selectedJob) {
        res = await updateJob(selectedJob._id, jobData);
      } else {
        res = await createJob(jobData);
      }

      if (res && !res.error) {
        setSuccess(true);
        setSelectedJob(null);
        resetForm();
        setTimeout(() => {
          setSuccess(false);
          setShowForm(false);
        }, 2000);
      } else {
        setError(res?.error || `Job ${selectedJob ? 'update' : 'creation'} failed`);
      }
    } catch (err) {
      setError('Server error. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setForm({
      title: '',
      description: '',
      requirements: [],
      techStack: [],
      category: '',
      jobType: 'Full-time',
      experienceLevel: 'Mid',
      salary: { min: 0, max: 0, currency: 'INR' },
      location: '',
      workMode: 'Remote',
      applicationDeadline: '',
      maxApplications: 100,
      hasTest: false,
    });
  };

  const handleEdit = (job) => {
    setSelectedJob(job);
    setForm({
      title: job.title || '',
      description: job.description || '',
      requirements: job.requirements || [],
      techStack: job.techStack || [],
      category: job.category || '',
      jobType: job.jobType || 'Full-time',
      experienceLevel: job.experienceLevel || 'Mid',
      salary: job.salary || { min: 0, max: 0, currency: 'INR' },
      location: job.location || '',
      workMode: job.workMode || 'Remote',
      applicationDeadline: job.applicationDeadline ? new Date(job.applicationDeadline).toISOString().slice(0, 16) : '',
      maxApplications: job.maxApplications || 100,
      hasTest: job.hasTest || false,
    });
    setShowForm(true);
  };

  const handleStatusUpdate = async (jobId, isActive) => {
    try {
      setLoading(true);

      // Show loading toast
      const loadingToast = toast.loading(`${isActive ? 'Activating' : 'Deactivating'} job...`);

      const result = await updateJobStatus(jobId, isActive);

      if (result) {
        // Refresh jobs list to get updated data
        await getJobs();

        // Show success toast
        toast.success(`Job ${isActive ? 'activated' : 'deactivated'} successfully!`, {
          id: loadingToast,
          duration: 3000,
          icon: isActive ? '✅' : '⏸️',
        });
      } else {
        toast.error('Failed to update job status. Please try again.', {
          id: loadingToast,
          duration: 4000,
        });
      }
    } catch (error) {
      console.error('Error updating job status:', error);
      toast.error('Failed to update job status. Please try again.', {
        duration: 4000,
      });
      setError('Failed to update job status. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleViewApplications = (jobId) => {
    const job = jobs.find(j => j._id === jobId);
    console.log('Selected job for candidates:', job);
    console.log('Job applicants:', job?.applicants);
    if (job) {
      setSelectedJobForCandidates(job);
      setShowCandidatesModal(true);
    }
  };

  const handleCloseCandidatesModal = () => {
    setShowCandidatesModal(false);
    setSelectedJobForCandidates(null);
  };

  const handleCloseForm = useCallback(() => {
    setShowForm(false);
    setSelectedJob(null);
    setError(null);
    setSuccess(false);
    resetForm();
  }, []);

  const handleKeyDown = useCallback(
    (e) => {
      if (e.key === 'Escape' && showForm) handleCloseForm();
    },
    [showForm, handleCloseForm]
  );

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  // Filter jobs based on search and category
  const filteredJobs = jobs?.filter(job => {
    const matchesSearch = job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      job.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      job.location.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !filterCategory || job.category === filterCategory;
    return matchesSearch && matchesCategory;
  }) || [];

  // Statistics
  const stats = {
    totalJobs: jobs?.length || 0,
    activeJobs: jobs?.filter(job => new Date(job.applicationDeadline) > new Date()).length || 0,
    totalApplications: jobs?.reduce((sum, job) => sum + (job.applications || 0), 0) || 0,
  };

  if (isCheckingAuth) {
    return (
      <div className="h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="relative">
            <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
            <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-blue-400 rounded-full animate-pulse mx-auto"></div>
          </div>
          <p className="text-lg font-semibold text-gray-700">Checking access...</p>
          <p className="text-sm text-gray-500 mt-1">Please wait while we verify your credentials</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-[rgb(35,65,75)] to-gray-700 rounded-lg flex items-center justify-center">
              <BriefcaseIcon className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Job Management</h1>
              <p className="text-sm text-gray-600">Create, manage, and monitor your job postings</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={() => setShowForm(true)}
              className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-[rgb(35,65,75)] to-gray-700 hover:from-[rgb(35,65,75)]/90 hover:to-gray-700/90 text-white rounded-lg transition-all duration-200 font-medium shadow-lg hover:shadow-xl"
            >
              <PlusCircleIcon className="h-5 w-5" />
              Create Job
            </button>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <motion.div
            className="text-center"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-3">
              <BriefcaseIcon className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900">{stats.totalJobs}</h3>
            <p className="text-gray-600">Total Jobs Posted</p>
          </motion.div>

          <motion.div
            className="text-center"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-3">
              <CheckCircleIcon className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900">{stats.activeJobs}</h3>
            <p className="text-gray-600">Active Listings</p>
          </motion.div>

          <motion.div
            className="text-center"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-3">
              <UserGroupIcon className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900">{stats.totalApplications}</h3>
            <p className="text-gray-600">Total Applications</p>
          </motion.div>
        </div>
      </div>

      {/* Job Form Modal */}
      {showForm && (
        <JobFormModal
          showForm={showForm}
          handleCloseForm={handleCloseForm}
          handleSubmit={handleSubmit}
          handleChange={handleChange}
          handleRequirementChange={handleRequirementChange}
          handleTechStackChange={handleTechStackChange}
          form={form}
          error={error}
          success={success}
          loading={loading}
          isEditing={!!selectedJob}
        />
      )}

      {/* Existing Jobs Section */}
      <ExistingJobsSection
        jobs={filteredJobs}
        onEdit={handleEdit}
        onStatusUpdate={handleStatusUpdate}
        onViewApplications={handleViewApplications}
        onViewDetails={(jobId) => {
          // Add view details functionality here
          console.log('View details:', jobId);
        }}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        filterCategory={filterCategory}
        setFilterCategory={setFilterCategory}
      />

      {/* Test Button - Remove this after testing */}
      {/* {jobs.length > 0 && (
        <div className="fixed bottom-4 right-4 z-50">
          <button
            onClick={() => {
              const testJob = jobs[0]; // Use first job for testing
              console.log('Test job:', testJob);
              setSelectedJobForCandidates(testJob);
              setShowCandidatesModal(true);
            }}
            className="bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-red-600"
          >
            Test Candidates Modal
          </button>
        </div>
      )} */}

      {/* Job Candidates Modal */}
      <JobCandidatesModal
        isOpen={showCandidatesModal}
        onClose={handleCloseCandidatesModal}
        job={selectedJobForCandidates}
      />
    </div>
  );
};

export default CreateJob;